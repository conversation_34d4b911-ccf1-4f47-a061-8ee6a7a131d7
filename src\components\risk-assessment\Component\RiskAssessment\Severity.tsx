import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp, AlertCircle } from 'lucide-react';

interface SeverityOption {
  value: string;
  label: string;
}

interface SeverityData {
  id: string;
  severity: string;
  personnel: string;
  property: string;
  environment: string;
  serviceLoss: string;
}

interface TaskItem {
  severity: string | number;
  [key: number]: any;
}

interface SeverityProps {
  severity: SeverityOption[];
  severityData: SeverityData[];
  required: boolean;
  onChangeSeverity: (option: { value: string; label: string }, type?: string) => void;
  item: TaskItem[];
}

const Severity: React.FC<SeverityProps> = ({ 
  severity, 
  severityData, 
  required, 
  onChangeSeverity, 
  item 
}) => {
  const [severityTable, setSeverityTable] = useState<boolean>(false);
  const hasError = required === false && item[4]?.severity === '';

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
          <div className="md:col-span-2">
            <h6 className="font-semibold">Severity</h6>
            <p className="text-sm text-muted-foreground italic">
              Degree of harm or impact that could result from a hazardous event or situation
            </p>
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
              <span>Select Severity Level</span>
              {hasError && <AlertCircle className="w-4 h-4 text-red-500" />}
            </Label>
            <Select
              value={String(item[4]?.severity)}
              onValueChange={(value) => {
                const option = severity.find(s => s.value === value);
                if (option) onChangeSeverity(option, 'assessment');
              }}
            >
              <SelectTrigger className={`h-11 ${hasError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}>
                <SelectValue placeholder="Select severity level" />
              </SelectTrigger>
              <SelectContent>
                {severity.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {hasError && (
              <p className="text-xs text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-3 h-3" />
                <span>Please select a severity level</span>
              </p>
            )}
          </div>
        </div>

        <Collapsible open={severityTable} onOpenChange={setSeverityTable}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
              Understand Severity Levels
              {severityTable ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-4">
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Severity Level</TableHead>
                    <TableHead>Descriptor</TableHead>
                    <TableHead>Personnel</TableHead>
                    <TableHead>Equipment / Property</TableHead>
                    <TableHead>Environment</TableHead>
                    <TableHead>Service Loss</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {severityData.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.severity}</TableCell>
                      <TableCell>{row.personnel}</TableCell>
                      <TableCell>{row.property}</TableCell>
                      <TableCell>{row.environment}</TableCell>
                      <TableCell>{row.serviceLoss}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export default Severity;
