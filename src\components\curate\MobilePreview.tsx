import React from "react";
import { DroppedItem } from "@/types/curate";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import ComponentPreview from "./ComponentPreview";
import { Smartphone } from "lucide-react";

interface MobilePreviewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: DroppedItem[];
}

const MobilePreview: React.FC<MobilePreviewProps> = ({
  open,
  onOpenChange,
  items,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden h-[80vh] max-h-[700px]">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Mobile Preview
          </DialogTitle>
        </DialogHeader>
        
        <div className="relative w-full h-full bg-slate-100 dark:bg-slate-800 overflow-hidden">
          {/* Phone frame */}
          <div className="absolute inset-0 pointer-events-none border-8 border-black rounded-[40px] z-10"></div>
          
          {/* Status bar */}
          <div className="h-6 bg-black"></div>
          
          {/* Content area */}
          <ScrollArea className="h-[calc(100%-6px)] bg-white dark:bg-slate-900">
            <div className="p-4">
              {items.length === 0 ? (
                <div className="text-center p-8 text-muted-foreground">
                  <p>No components to preview</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {items.map((item) => (
                    <div key={item.id} className="border rounded-md p-4 bg-white dark:bg-slate-800 shadow-sm">
                      <ComponentPreview item={item} isPreview={true} />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
          
          {/* Home indicator */}
          <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full z-10"></div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MobilePreview;
