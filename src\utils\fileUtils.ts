import apiService from '@/services/apiService';

/**
 * Uploads a file and returns the file information including the original name
 * @param file The file to upload
 * @returns Promise with file upload response
 */
export const uploadFile = async (file: File) => {
  try {
    const response = await apiService.uploadFileToFiles(file);
    
    if (response.files && response.files.length > 0) {
      const uploadedFile = response.files[0];
      return {
        originalName: uploadedFile.originalname,
        fileName: uploadedFile.originalname, // Use original name as the file identifier
        size: uploadedFile.size,
        mimetype: uploadedFile.mimetype,
        success: true
      };
    } else {
      throw new Error('No file uploaded');
    }
  } catch (error) {
    console.error('File upload failed:', error);
    throw error;
  }
};

/**
 * Gets the presigned URL for displaying a file
 * @param fileName The original file name returned from upload
 * @returns Promise with the presigned URL
 */
export const getFileDisplayUrl = async (fileName: string): Promise<string> => {
  try {
    const presignedUrl = await apiService.getFileDownloadUrl(fileName);
    return presignedUrl;
  } catch (error) {
    console.error('Failed to get file display URL:', error);
    throw error;
  }
};

/**
 * Validates file type and size
 * @param file The file to validate
 * @param allowedTypes Array of allowed MIME types
 * @param maxSizeInMB Maximum file size in MB
 * @returns Validation result
 */
export const validateFile = (
  file: File, 
  allowedTypes: string[] = [], 
  maxSizeInMB: number = 50
): { isValid: boolean; error?: string } => {
  // Check file size
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    return {
      isValid: false,
      error: `File size must be less than ${maxSizeInMB}MB`
    };
  }

  // Check file type if specified
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed`
    };
  }

  return { isValid: true };
};

/**
 * Common file type constants
 */
export const FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  VIDEOS: ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'],
  AUDIO: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/m4a'],
  DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  SCORM: ['application/zip', 'application/x-zip-compressed'],
  WEBGL: ['application/zip', 'application/x-zip-compressed']
};

/**
 * Format file size for display
 * @param bytes File size in bytes
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
