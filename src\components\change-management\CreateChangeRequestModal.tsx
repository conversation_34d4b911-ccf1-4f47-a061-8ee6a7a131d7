import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { CreateChangeRequestData } from '@/types/changeManagement';

const changeRequestSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  changeType: z.enum(['Operational', 'Technical', 'Organizational', 'Emergency']),
  priority: z.enum(['Low', 'Medium', 'High', 'Critical']),
  proposedImplementationDate: z.string().min(1, 'Implementation date is required'),
  businessJustification: z.string().min(1, 'Business justification is required'),
  impactDescription: z.string().min(1, 'Impact description is required'),
  rollbackPlan: z.string().min(1, 'Rollback plan is required'),
  estimatedCost: z.number().optional(),
  estimatedDuration: z.number().optional(),
});

interface CreateChangeRequestModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateChangeRequestData) => void;
}

const CreateChangeRequestModal: React.FC<CreateChangeRequestModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
}) => {
  const [affectedSystems, setAffectedSystems] = useState<string[]>([]);
  const [affectedDepartments, setAffectedDepartments] = useState<string[]>([]);
  const [newSystem, setNewSystem] = useState('');
  const [newDepartment, setNewDepartment] = useState('');

  const form = useForm<z.infer<typeof changeRequestSchema>>({
    resolver: zodResolver(changeRequestSchema),
    defaultValues: {
      title: '',
      description: '',
      changeType: 'Operational',
      priority: 'Medium',
      proposedImplementationDate: '',
      businessJustification: '',
      impactDescription: '',
      rollbackPlan: '',
    },
  });

  const handleSubmit = (values: z.infer<typeof changeRequestSchema>) => {
    const changeRequestData: CreateChangeRequestData = {
      ...values,
      affectedSystems,
      affectedDepartments,
    };
    onSubmit(changeRequestData);
    form.reset();
    setAffectedSystems([]);
    setAffectedDepartments([]);
    onOpenChange(false);
  };

  const addSystem = () => {
    if (newSystem.trim() && !affectedSystems.includes(newSystem.trim())) {
      setAffectedSystems([...affectedSystems, newSystem.trim()]);
      setNewSystem('');
    }
  };

  const removeSystem = (system: string) => {
    setAffectedSystems(affectedSystems.filter(s => s !== system));
  };

  const addDepartment = () => {
    if (newDepartment.trim() && !affectedDepartments.includes(newDepartment.trim())) {
      setAffectedDepartments([...affectedDepartments, newDepartment.trim()]);
      setNewDepartment('');
    }
  };

  const removeDepartment = (department: string) => {
    setAffectedDepartments(affectedDepartments.filter(d => d !== department));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Change Request</DialogTitle>
          <DialogDescription>
            Submit a new change request for review and approval.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter change request title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="changeType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Change Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select change type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Operational">Operational</SelectItem>
                        <SelectItem value="Technical">Technical</SelectItem>
                        <SelectItem value="Organizational">Organizational</SelectItem>
                        <SelectItem value="Emergency">Emergency</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Low">Low</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="proposedImplementationDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proposed Implementation Date</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Cost (Optional)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Enter estimated cost"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Duration (Hours)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Enter estimated duration"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Provide a detailed description of the change"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="businessJustification"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Business Justification</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Explain the business need for this change"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="impactDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Impact Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe the expected impact of this change"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rollbackPlan"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rollback Plan</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe the plan to rollback if issues occur"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Affected Systems */}
            <div className="space-y-3">
              <FormLabel>Affected Systems</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add affected system"
                  value={newSystem}
                  onChange={(e) => setNewSystem(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSystem())}
                />
                <Button type="button" onClick={addSystem} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {affectedSystems.map((system) => (
                  <Badge key={system} variant="secondary" className="flex items-center gap-1">
                    {system}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeSystem(system)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Affected Departments */}
            <div className="space-y-3">
              <FormLabel>Affected Departments</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add affected department"
                  value={newDepartment}
                  onChange={(e) => setNewDepartment(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDepartment())}
                />
                <Button type="button" onClick={addDepartment} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {affectedDepartments.map((department) => (
                  <Badge key={department} variant="secondary" className="flex items-center gap-1">
                    {department}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeDepartment(department)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Create Change Request
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateChangeRequestModal;
