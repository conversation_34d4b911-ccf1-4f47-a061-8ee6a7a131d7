import { useState } from "react";
import { Download, FileText, FileSpreadsheet, File } from "lucide-react";
import * as ExcelJS from 'exceljs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

interface TrendDataPoint {
  date: string;
  value: number;
}

interface ExportButtonProps {
  data: TrendDataPoint[];
  title: string;
  unit: string;
  targetPercentage: number;
  isImproving: boolean;
}

const ExportButton = ({ data, title, unit, targetPercentage, isImproving }: ExportButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr + '-01');
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short' 
    });
  };

  // Prepare data for export
  const prepareExportData = () => {
    return data.map((dataPoint, index) => {
      const previousValue = index > 0 ? data[index - 1].value : null;
      const change = previousValue !== null ? ((dataPoint.value - previousValue) / previousValue) * 100 : null;
      const isPositiveChange = change !== null && change >= 0;
      
      return {
        Period: formatDate(dataPoint.date),
        Value: `${dataPoint.value > 1000 ? `${(dataPoint.value/1000).toFixed(1)}k` : dataPoint.value.toFixed(1)} ${unit}`,
        'Raw Value': dataPoint.value,
        Change: change !== null ? `${isPositiveChange ? '+' : ''}${change.toFixed(1)}%` : '-',
        'Change %': change,
        Status: change !== null ? (isPositiveChange ? 'Improving' : 'Declining') : 'Baseline'
      };
    });
  };

  // Export to CSV
  const exportToCSV = () => {
    const exportData = prepareExportData();
    const headers = ['Period', 'Value', 'Change', 'Status'];
    const csvContent = [
      [`Detailed Data - ${title}`],
      [`Target: ${isImproving ? '+' : ''}${targetPercentage}%`],
      [`Generated: ${new Date().toLocaleDateString()}`],
      [],
      headers,
      ...exportData.map(row => [row.Period, row.Value, row.Change, row.Status])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    setIsOpen(false);
  };

  // Export to Excel
  const exportToExcel = async () => {
    const exportData = prepareExportData();
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Data');

    // Add metadata
    worksheet.addRow([`Detailed Data - ${title}`]);
    worksheet.addRow([`Target: ${isImproving ? '+' : ''}${targetPercentage}%`]);
    worksheet.addRow([`Generated: ${new Date().toLocaleDateString()}`]);
    worksheet.addRow([]); // Empty row

    // Add headers
    const headers = ['Period', 'Value', 'Change', 'Status'];
    worksheet.addRow(headers);

    // Add data
    exportData.forEach(row => {
      worksheet.addRow([row.Period, row.Value, row.Change, row.Status]);
    });

    // Style the header row
    const headerRow = worksheet.getRow(5);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    // Generate buffer and download
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.xlsx`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    setIsOpen(false);
  };

  // Export to PDF
  const exportToPDF = () => {
    const exportData = prepareExportData();
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(16);
    doc.text(`Detailed Data - ${title}`, 20, 20);
    
    // Add metadata
    doc.setFontSize(10);
    doc.text(`Target: ${isImproving ? '+' : ''}${targetPercentage}%`, 20, 30);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 35);
    
    // Add table
    autoTable(doc, {
      head: [['Period', 'Value', 'Change', 'Status']],
      body: exportData.map(row => [row.Period, row.Value, row.Change, row.Status]),
      startY: 45,
      styles: { fontSize: 9 },
      headStyles: { fillColor: [59, 130, 246] }
    });
    
    doc.save(`${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.pdf`);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium text-sm"
      >
        <Download className="w-4 h-4" />
        <span>Export Data</span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Menu */}
          <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50 py-2">
            <button
              onClick={exportToCSV}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <FileText className="w-4 h-4 text-green-600" />
              <span>Export as CSV</span>
            </button>
            
            <button
              onClick={exportToExcel}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <FileSpreadsheet className="w-4 h-4 text-blue-600" />
              <span>Export as Excel</span>
            </button>
            
            <button
              onClick={exportToPDF}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <File className="w-4 h-4 text-red-600" />
              <span>Export as PDF</span>
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ExportButton;
