import React from "react";
import { Badge } from "@/components/ui/badge";
import DocumentDraggableItem from "./DocumentDraggableItem";
import {
  FileText,
  Heading1,
  Heading2,
  Type,
  Image,
  List,
  Quote,
  Minus,
  Link,
  Video
} from "lucide-react";

interface DocumentSidebarProps {
  onDragStart: (type: string) => void;
  onComponentClick?: (type: string) => void;
}

const DocumentSidebar: React.FC<DocumentSidebarProps> = ({ onDragStart, onComponentClick }) => {
  // Document-specific components
  const documentComponents = [
    // Content Structure Components
    { type: 'document-header', icon: <Heading1 className="h-5 w-5" />, label: 'Document Header', description: 'Main document title' },
    { type: 'section-header', icon: <Heading2 className="h-5 w-5" />, label: 'Section Header', description: 'Section title' },
    { type: 'paragraph', icon: <Type className="h-5 w-5" />, label: 'Paragraph', description: 'Text content block' },
    { type: 'bullet-list', icon: <List className="h-5 w-5" />, label: 'Bullet List', description: 'Bulleted list items' },
    { type: 'numbered-list', icon: <List className="h-5 w-5" />, label: 'Numbered List', description: 'Numbered list items' },
    { type: 'quote', icon: <Quote className="h-5 w-5" />, label: 'Quote Block', description: 'Highlighted quote' },
    { type: 'separator', icon: <Minus className="h-5 w-5" />, label: 'Separator', description: 'Visual divider' },

    // Media Components
    { type: 'image', icon: <Image className="h-5 w-5" />, label: 'Image/File Upload', description: 'Upload images or any files' },
    { type: 'video', icon: <Video className="h-5 w-5" />, label: 'Video Upload', description: 'Upload video files (max 20MB)' },
    { type: 'file-attachment', icon: <FileText className="h-5 w-5" />, label: 'File Attachment', description: 'Upload any file type' },

    // Interactive Components
    { type: 'link', icon: <Link className="h-5 w-5" />, label: 'Link', description: 'Hyperlink' },
  ];

  // Group components by category
  const componentCategories = [
    {
      name: "Content Structure",
      components: documentComponents.slice(0, 7),
      color: "blue"
    },
    {
      name: "Media & Files",
      components: documentComponents.slice(7, 10),
      color: "green"
    },
    {
      name: "Interactive Elements",
      components: documentComponents.slice(10),
      color: "purple"
    }
  ];

  const getCategoryBadgeColor = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'green':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'purple':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'orange':
        return 'bg-orange-100 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="w-80 bg-gradient-to-b from-white via-slate-50 to-slate-100 dark:from-slate-800 dark:via-slate-850 dark:to-slate-900 border-r border-slate-200 dark:border-slate-700 shadow-xl h-full flex flex-col flex-shrink-0 overflow-hidden">
      {/* Header Section */}
      <div className="flex-shrink-0 p-4 bg-gradient-to-r from-slate-50 via-white to-slate-50 dark:from-slate-800 dark:via-slate-750 dark:to-slate-800 border-b border-slate-100 dark:border-slate-700">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold text-sm text-slate-700 dark:text-slate-300">
            Document Components
          </h4>
          <Badge
            variant="secondary"
            className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 border-blue-200 dark:border-blue-800"
          >
            {documentComponents.length} items
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            Drag components to build your document
          </p>
        </div>
      </div>

      {/* Components List by Category */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {componentCategories.map((category, categoryIndex) => (
          <div key={category.name} className="border-b border-slate-100 dark:border-slate-700 last:border-b-0">
            {/* Category Header */}
            <div className="sticky top-0 bg-slate-50 dark:bg-slate-800 p-3 border-b border-slate-100 dark:border-slate-700">
              <div className="flex items-center justify-between">
                <h5 className="font-medium text-xs text-slate-600 dark:text-slate-400 uppercase tracking-wide">
                  {category.name}
                </h5>
                <Badge
                  variant="outline"
                  className={`text-xs ${getCategoryBadgeColor(category.color)}`}
                >
                  {category.components.length}
                </Badge>
              </div>
            </div>

            {/* Category Components */}
            <div className="p-3 space-y-2">
              {category.components.map((component, index) => (
                <div
                  key={component.type}
                  className="transform transition-all duration-200 hover:scale-102"
                  style={{ animationDelay: `${(categoryIndex * 100) + (index * 50)}ms` }}
                >
                  <DocumentDraggableItem
                    type={component.type}
                    icon={component.icon}
                    label={component.label}
                    description={component.description}
                    onDragStart={onDragStart}
                    onComponentClick={onComponentClick}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 border-t border-slate-200 dark:border-slate-700">
        <div className="text-center">
          <p className="text-xs text-slate-500 dark:text-slate-400 mb-1">
            📄 Tip: Build professional documents with drag & drop
          </p>
          <div className="flex justify-center space-x-1">
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-1 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentSidebar;
