import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Circle } from 'lucide-react';

interface StageStatus {
  hazardsIdentification: string;
  consequences: string;
  currentControls: string;
  riskEstimation: string;
  additionalControls: string;
}

interface HeadStepperProps {
  stages: string[];
  stageStatus: StageStatus;
  activeStage: number;
  handleStageClick: (index: number) => void;
  getStatusClass: (status: string) => string;
}

const HeadStepper: React.FC<HeadStepperProps> = ({ 
  stages, 
  stageStatus, 
  activeStage, 
  handleStageClick, 
  getStatusClass 
}) => {
  const getStatusKeyByIndex = (index: number): string => {
    switch (index) {
      case 0:
        return stageStatus.hazardsIdentification;
      case 1:
        return stageStatus.consequences;
      case 2:
        return stageStatus.currentControls;
      case 3:
        return stageStatus.riskEstimation;
      case 4:
        return stageStatus.additionalControls;
      default:
        return '';
    }
  };

  const getStepIcon = (status: string) => {
    if (status === 'completed') return <CheckCircle className="h-4 w-4" />;
    if (status === 'inprogress') return <Clock className="h-4 w-4" />;
    return <Circle className="h-4 w-4" />;
  };

  const getStepVariant = (status: string, isActive: boolean) => {
    if (status === 'completed') return 'default';
    if (status === 'inprogress') return 'secondary';
    if (isActive) return 'outline';
    return 'outline';
  };

  return (
    <div className="space-y-4">
      {/* Stepper */}
      <div className="relative w-full">
        {/* Container for circles with proper alignment */}
        <div className="flex items-start justify-between w-full relative">
          {/* Background Line - positioned to go through center of circles */}
          <div className="absolute top-5 left-0 right-0 h-0.5 bg-gray-300 z-0"
               style={{
                 left: `${100 / (stages.length * 2)}%`,
                 right: `${100 / (stages.length * 2)}%`
               }}></div>

          {/* Progress Line */}
          <div
            className="absolute top-5 h-0.5 bg-green-500 z-0 transition-all duration-300"
            style={{
              left: `${100 / (stages.length * 2)}%`,
              width: `${(stages.filter((_, i) => getStatusKeyByIndex(i) === 'completed').length / (stages.length - 1)) * (100 - (200 / stages.length))}%`
            }}
          ></div>

          {stages.map((stage, index) => {
            const stageKey = getStatusKeyByIndex(index);
            const isActive = activeStage === index;
            const isCompleted = stageKey === 'completed';
            const isInProgress = stageKey === 'inprogress';

            return (
              <div
                key={index}
                className="flex flex-col items-center cursor-pointer group z-10 relative"
                onClick={() => handleStageClick(index)}
                style={{ width: `${100 / stages.length}%` }}
              >
                {/* Step Circle */}
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors relative z-20
                  ${isCompleted ? 'bg-green-500 border-green-500 hover:bg-green-600 hover:border-green-600' :
                    isInProgress ? 'bg-orange-500 border-orange-500 hover:bg-orange-600 hover:border-orange-600' :
                    isActive ? 'border-primary text-primary bg-white group-hover:border-primary group-hover:text-primary' :
                    'border-gray-300 text-gray-400 bg-white group-hover:border-primary group-hover:text-primary'}
                `}>
                  {isCompleted ? (
                    <CheckCircle className="h-5 w-5 text-white" />
                  ) : isInProgress ? (
                    <Clock className="h-5 w-5 text-white" />
                  ) : (
                    <span className="text-sm font-semibold">{index + 1}</span>
                  )}
                </div>

                {/* Step Title */}
                <div className={`
                  mt-2 text-xs text-center max-w-20 leading-tight
                  ${isActive ? 'text-primary font-medium' : 'text-gray-600'}
                  group-hover:text-primary
                `}>
                  {stage}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="flex justify-end gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span>Finalized</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-orange-500"></div>
          <span>Drafted</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-gray-400"></div>
          <span>No information entered</span>
        </div>
      </div>
    </div>
  );
};

export default HeadStepper;
