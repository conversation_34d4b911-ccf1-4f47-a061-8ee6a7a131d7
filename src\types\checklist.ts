export type ContentMode = "communicate" | "evaluate" | "observe" | "document";

export interface AttachmentConfig {
  image: {
    enabled: boolean;
    galleryUploads: boolean;
  };
  video: {
    enabled: boolean;
    galleryUploads: boolean;
  };
  documents: {
    enabled: boolean;
    // Documents are always from device by default
  };
}

export interface ContentComponent {
  id: string;
  type: string;
  title: string;
  description?: string;
  required?: boolean;
  position: number;
  options?: string[];
  placeholder?: string;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  step?: number;
  multiple?: boolean;
  accept?: string;
  rows?: number;
  cols?: number;
  format?: string;
  attachmentConfig?: AttachmentConfig;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
}

export interface DroppedItem {
  id: string;
  type: string;
  data: ContentComponent;
}

export interface ChecklistTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  version: string;
  items: DroppedItem[];
  created: string;
  updated: string;
}
