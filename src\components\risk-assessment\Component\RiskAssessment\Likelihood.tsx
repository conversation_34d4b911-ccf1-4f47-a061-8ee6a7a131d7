import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp, AlertCircle } from 'lucide-react';

interface LikelihoodOption {
  value: string;
  label: string;
}

interface LevelData {
  level: string;
  descriptor: string;
  detailedDescription: string;
}

interface TaskItem {
  likelyhood: string | number;
  [key: number]: any;
}

interface LikelihoodProps {
  likelyhood: LikelihoodOption[];
  levelData: LevelData[];
  required: boolean;
  onChangeLikelyhood: (option: { value: string; label: string }, type?: string) => void;
  item: TaskItem[];
  rowClassName: (data: any) => string;
}

const Likelihood: React.FC<LikelihoodProps> = ({ 
  likelyhood, 
  levelData, 
  required, 
  onChangeLikelyhood, 
  item, 
  rowClassName 
}) => {
  const [likelyhoodTable, setLikelyhoodTable] = useState<boolean>(false);
  const hasError = required === false && item[4]?.likelyhood === '';

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
          <div className="md:col-span-2">
            <h6 className="font-semibold">Likelihood</h6>
            <p className="text-sm text-muted-foreground italic">
              Frequency with which a hazardous event or situation could happen
            </p>
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
              <span>Select Likelihood Level</span>
              {hasError && <AlertCircle className="w-4 h-4 text-red-500" />}
            </Label>
            <Select
              value={String(item[4]?.likelyhood)}
              onValueChange={(value) => {
                const option = likelyhood.find(l => l.value === value);
                if (option) onChangeLikelyhood(option, 'assessment');
              }}
            >
              <SelectTrigger className={`h-11 ${hasError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}>
                <SelectValue placeholder="Select likelihood level" />
              </SelectTrigger>
              <SelectContent>
                {likelyhood.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {hasError && (
              <p className="text-xs text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-3 h-3" />
                <span>Please select a likelihood level</span>
              </p>
            )}
          </div>
        </div>

        <Collapsible open={likelyhoodTable} onOpenChange={setLikelyhoodTable}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
              Understand Likelihood Levels
              {likelyhoodTable ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-4">
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Level</TableHead>
                    <TableHead>Descriptor</TableHead>
                    <TableHead>Detailed Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {levelData.map((row, index) => (
                    <TableRow key={index} className={rowClassName ? rowClassName(row) : ''}>
                      <TableCell>{row.level}</TableCell>
                      <TableCell>{row.descriptor}</TableCell>
                      <TableCell>{row.detailedDescription}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export default Likelihood;
