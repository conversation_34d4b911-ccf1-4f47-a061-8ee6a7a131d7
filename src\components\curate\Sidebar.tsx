import React from "react";
import { ContentMode } from "@/types/curate";
import ModeToggle from "./ModeToggle";
import DraggableItem from "./DraggableItem";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Image,
  FileVideo,
  Video,
  Link,
  FileText,
  FileAudio,
  FileArchive,
  Code,
  Boxes,
  Gamepad2,
  ListChecks,
  FormInput,
  Upload,
  FileQuestion,
  CheckSquare,
  Star,
  Calendar,
  Clock,
  Timer,
  Phone,
  Hash,
  PenTool,
} from "lucide-react";

interface SidebarProps {
  activeMode: ContentMode;
  onChange: (mode: ContentMode) => void;
  onDragStart: (type: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  activeMode,
  onChange,
  onDragStart,
}) => {
  const contentComponents = [
    {
      type: "image",
      icon: <Image className="h-5 w-5 text-blue-500" />,
      label: "Image",
      description: "Upload or select from library",
    },
    {
      type: "video",
      icon: <FileVideo className="h-5 w-5 text-blue-500" />,
      label: "Video",
      description: "Upload or select from library",
    },
    {
      type: "youtube",
      icon: <Video className="h-5 w-5 text-red-500" />,
      label: "YouTube",
      description: "Embed a YouTube video",
    },
    {
      type: "weblink",
      icon: <Link className="h-5 w-5 text-green-500" />,
      label: "Web Link",
      description: "Add a link to external content",
    },
    {
      type: "text",
      icon: <FileText className="h-5 w-5 text-purple-500" />,
      label: "Text",
      description: "Rich text editor",
    },
    {
      type: "audio",
      icon: <FileAudio className="h-5 w-5 text-amber-500" />,
      label: "Audio",
      description: "Upload or record audio",
    },
    {
      type: "attachment",
      icon: <FileArchive className="h-5 w-5 text-slate-500" />,
      label: "Attachment",
      description: "Upload any file type",
    },
    {
      type: "embed",
      icon: <Code className="h-5 w-5 text-indigo-500" />,
      label: "Embed",
      description: "Embed HTML or iframe code",
    },
    {
      type: "scorm",
      icon: <Boxes className="h-5 w-5 text-orange-500" />,
      label: "SCORM",
      description: "Upload SCORM package",
    },
    {
      type: "webgl",
      icon: <Gamepad2 className="h-5 w-5 text-cyan-500" />,
      label: "WebGL",
      description: "Interactive 3D content",
    },
  ];

  const formComponents = [
    {
      type: "mcq",
      icon: <ListChecks className="h-5 w-5 text-blue-500" />,
      label: "Multiple Choice",
      description: "Multiple choice questions",
    },
    {
      type: "textbox",
      icon: <FormInput className="h-5 w-5 text-green-500" />,
      label: "Text Input",
      description: "Single or multi-line text input",
    },
    {
      type: "feedback-image",
      icon: <Upload className="h-5 w-5 text-purple-500" />,
      label: "Image Upload",
      description: "Allow users to upload images",
    },
    {
      type: "option",
      icon: <FileQuestion className="h-5 w-5 text-amber-500" />,
      label: "Option",
      description: "Radio button options",
    },
    {
      type: "checkbox",
      icon: <CheckSquare className="h-5 w-5 text-indigo-500" />,
      label: "Checkbox",
      description: "Multiple selection checkboxes",
    },
    {
      type: "star",
      icon: <Star className="h-5 w-5 text-yellow-500" />,
      label: "Rating",
      description: "Star rating component",
    },
    {
      type: "date",
      icon: <Calendar className="h-5 w-5 text-red-500" />,
      label: "Date",
      description: "Date picker component",
    },
    {
      type: "time",
      icon: <Clock className="h-5 w-5 text-cyan-500" />,
      label: "Time",
      description: "Time picker component",
    },
    {
      type: "duration",
      icon: <Timer className="h-5 w-5 text-slate-500" />,
      label: "Duration",
      description: "Duration input component",
    },
    {
      type: "phone",
      icon: <Phone className="h-5 w-5 text-emerald-500" />,
      label: "Phone",
      description: "Phone number input",
    },
    {
      type: "alphanumeric",
      icon: <Hash className="h-5 w-5 text-violet-500" />,
      label: "Alphanumeric",
      description: "Custom pattern input",
    },
    {
      type: "sign",
      icon: <PenTool className="h-5 w-5 text-orange-500" />,
      label: "Signature",
      description: "Digital signature field",
    },
  ];

  const componentsToShow =
    activeMode === "communicate" ? contentComponents : formComponents;

  return (
    <div className="w-72 border-r border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 flex flex-col h-full shadow-sm">
      <div className="p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
        <ModeToggle activeMode={activeMode} onChange={onChange} />
      </div>
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <h2 className="text-base font-semibold mb-1 text-slate-800 dark:text-slate-200">
          {activeMode === "communicate" ? "Content Components" : "Form Components"}
        </h2>
        <p className="text-xs text-muted-foreground">
          Drag and drop components to the canvas
        </p>
      </div>
      <div className="flex-1 overflow-hidden flex flex-col">
        <div className="p-3 bg-slate-50 dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
          <div className="relative">
            <input
              type="text"
              placeholder="Search components..."
              className="w-full h-9 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pl-8"
            />
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 absolute left-2.5 top-2.5 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        <ScrollArea className="flex-1 overflow-y-auto">
          <div className="p-3 space-y-2">
            {componentsToShow.map((component) => (
              <DraggableItem
                key={component.type}
                type={component.type}
                icon={component.icon}
                label={component.label}
                description={component.description}
                onDragStart={onDragStart}
              />
            ))}
          </div>
        </ScrollArea>
        <div className="p-3 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 text-xs text-center text-muted-foreground">
          <p>Tip: You can also add components directly from the workspace</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
