
import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from 'react-redux';
import { store } from './store';
import PageLayout from "./components/layout/PageLayout";
import HomePage from "./pages/HomePage";
import ActionsPage from "./pages/ActionsPage";
import ObservationPage from "./pages/ObservationPage";
import UsersPage from "./pages/UsersPage";
import RoleAssignmentPage from "./pages/RoleAssignmentPage";
import RiskAssessmentPage from "./pages/RiskAssessmentPage";
import AddRiskAssessmentPage from "./pages/AddRiskAssessmentPage";
import NewRiskAssessmentPage from "./pages/NewRiskAssessmentPage";
import EditRiskAssessmentPage from "./pages/EditRiskAssessmentPage";
import OperationalTasksPage from "./pages/OperationalTasksPage";
import EPermitToWorkPage from "./pages/EPermitToWorkPage";
import IncidentInvestigationPage from "./pages/IncidentInvestigationPage";
import KnowledgePage from "./pages/KnowledgePage";
import ContentEditor from "./pages/ContentEditor";
import InspectionPage from "./pages/InspectionPage";
import SettingsPage from "./pages/SettingsPage";
import LogoutPage from "./pages/LogoutPage";
import NotFoundPage from "./pages/NotFoundPage";
import LoginPage from "./pages/LoginPage";
import ServicePage from "./pages/ServicePage";
import ChecklistCurationPage from "./pages/ChecklistCurationPage";
import DocumentPage from "./pages/DocumentPage";
import DocumentCurationPage from "./pages/DocumentCurationPage";
import CategoryDocumentsPage from "./pages/CategoryDocumentsPage";
import ChangeManagementPage from "./pages/ChangeManagementPage";
import AssetTrackingPage from "./pages/AssetTrackingPage";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import AuthInitializer from "./components/auth/AuthInitializer";

const queryClient = new QueryClient();

const App = () => {
  return (
    <Provider store={store}>
      <AuthInitializer>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<LoginPage />} />

                {/* Protected routes */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <PageLayout><HomePage /></PageLayout>
                  </ProtectedRoute>
                } />
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <PageLayout><HomePage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/actions" element={
                <ProtectedRoute>
                  <PageLayout><ActionsPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/observation" element={
                <ProtectedRoute>
                  <PageLayout><ObservationPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/observation" element={
                <ProtectedRoute>
                  <PageLayout><ObservationPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/risk-assessment" element={
                <ProtectedRoute>
                  <PageLayout><RiskAssessmentPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/risk" element={
                <ProtectedRoute>
                  <PageLayout><RiskAssessmentPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/risk-assessment/add" element={
                <ProtectedRoute>
                  <AddRiskAssessmentPage />
                </ProtectedRoute>
              } />
              <Route path="/risk-assessment/new" element={
                <ProtectedRoute>
                  <NewRiskAssessmentPage />
                </ProtectedRoute>
              } />
              <Route path="/risk-assessment/edit/:id" element={
                <ProtectedRoute>
                  <EditRiskAssessmentPage />
                </ProtectedRoute>
              } />
              <Route path="/apps/ott" element={
                <ProtectedRoute>
                  <PageLayout><OperationalTasksPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/eptw-gen" element={
                <ProtectedRoute>
                  <PageLayout><EPermitToWorkPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/investigation" element={
                <ProtectedRoute>
                  <PageLayout><IncidentInvestigationPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/knowledge" element={
                <ProtectedRoute>
                  <PageLayout><KnowledgePage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/knowledge/curate" element={
                <ProtectedRoute>
                  <ContentEditor />
                </ProtectedRoute>
              } />
              <Route path="/apps/doc" element={
                <ProtectedRoute>
                  <PageLayout><DocumentPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/inspection" element={
                <ProtectedRoute>
                  <PageLayout><InspectionPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/apps/cgm" element={
                <ProtectedRoute>
                  <PageLayout><ChangeManagementPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/checklists/:id/curate" element={
                <ProtectedRoute>
                  <ChecklistCurationPage />
                </ProtectedRoute>
              } />
              <Route path="/documents/:id/curate/:actionId" element={
                <ProtectedRoute>
                  <DocumentCurationPage />
                </ProtectedRoute>
              } />
              <Route path="/documents/:id/curate" element={
                <ProtectedRoute>
                  <DocumentCurationPage />
                </ProtectedRoute>
              } />
              <Route path="/apps/doc/category/:categoryId" element={
                <ProtectedRoute>
                  <CategoryDocumentsPage />
                </ProtectedRoute>
              } />
              <Route path="/users" element={
                <ProtectedRoute>
                  <PageLayout><UsersPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/role-assignment" element={
                <ProtectedRoute>
                  <PageLayout><RoleAssignmentPage /></PageLayout>
                </ProtectedRoute>
              } />
              <Route path="/settings" element={
                <ProtectedRoute>
                  <PageLayout><SettingsPage /></PageLayout>
                </ProtectedRoute>
              } />
              {/* <Route path="/test-charts" element={
                <ProtectedRoute>
                  <PageLayout><TestChartsPage /></PageLayout>
                </ProtectedRoute>
              } /> */}
              <Route path="/logout" element={<LogoutPage />} />

              {/* ATM specific route */}
              <Route path="/apps/atm" element={
                <ProtectedRoute>
                  <PageLayout><AssetTrackingPage /></PageLayout>
                </ProtectedRoute>
              } />

              {/* Dynamic route for service pages */}
              <Route path="/apps/:serviceUrl" element={
                <ProtectedRoute>
                  <PageLayout><ServicePage /></PageLayout>
                </ProtectedRoute>
              } />

              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
      </AuthInitializer>
    </Provider>
  );
};

export default App;
