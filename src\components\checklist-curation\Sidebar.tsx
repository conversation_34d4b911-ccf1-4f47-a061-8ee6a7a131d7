
import React from "react";
import { Badge } from "@/components/ui/badge";
import DraggableItem from "./DraggableItem";
import {
  Calendar,
  PenTool,
  Heading1,
  Heading2,
  Type,
  CheckCircle,
  ClipboardList,
  TextCursor,
  Paperclip
} from "lucide-react";

interface SidebarProps {
  onDragStart: (type: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onDragStart }) => {
  // Merged all components into a single array
  const allComponents = [
    // Content Components
    { type: 'header', icon: <Heading1 className="h-5 w-5" />, label: 'Header', description: 'Main page header' },
    { type: 'section-header', icon: <Heading2 className="h-5 w-5" />, label: 'Section Header', description: 'Section title' },
    { type: 'text-body', icon: <Type className="h-5 w-5" />, label: 'Text Body', description: 'Instructions & content' },

    // Form Components
    { type: 'sign', icon: <PenTool className="h-5 w-5" />, label: 'Sign', description: 'Signature pad' },
    { type: 'text-input', icon: <TextCursor className="h-5 w-5" />, label: 'Text Input', description: 'Text input field' },
    { type: 'attachment-input', icon: <Paperclip className="h-5 w-5" />, label: 'Attachment Input', description: 'File upload field' },
    { type: 'checkpoint', icon: <CheckCircle className="h-5 w-5" />, label: 'Checkpoint', description: 'Single checkpoint item' },
    { type: 'checkpoint-group', icon: <ClipboardList className="h-5 w-5" />, label: 'Checkpoint Group', description: 'Group of checkpoints' },
    { type: 'date', icon: <Calendar className="h-5 w-5" />, label: 'Date', description: 'Date selector' },
  ];

  return (
    <div className="w-80 bg-gradient-to-b from-white via-slate-50 to-slate-100 dark:from-slate-800 dark:via-slate-850 dark:to-slate-900 border-r border-slate-200 dark:border-slate-700 shadow-xl h-full flex flex-col">
      {/* Header Section */}
      {/* <div className="flex-shrink-0 p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-800 dark:via-slate-750 dark:to-slate-700 border-b border-slate-200 dark:border-slate-600">
        <div className="text-center">
          <h3 className="font-bold text-xl mb-2 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
            Component Library
          </h3>
          <p className="text-xs text-slate-600 dark:text-slate-400">
            Build your checklist with drag & drop
          </p>
        </div>
      </div> */}

      {/* Section Info */}
      <div className="flex-shrink-0 p-4 bg-gradient-to-r from-slate-50 via-white to-slate-50 dark:from-slate-800 dark:via-slate-750 dark:to-slate-800 border-b border-slate-100 dark:border-slate-700">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold text-sm text-slate-700 dark:text-slate-300">
             All Components
          </h4>
          <Badge
            variant="secondary"
            className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 border-blue-200 dark:border-blue-800"
          >
            {allComponents.length} items
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            Drag components to the canvas area
          </p>
        </div>
      </div>

      {/* Components List */}
      <div className="flex-1 p-4 space-y-3 overflow-y-auto">
        {allComponents.map((component, index) => (
          <div
            key={component.type}
            className="transform transition-all duration-200 hover:scale-102"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <DraggableItem
              type={component.type}
              icon={component.icon}
              label={component.label}
              description={component.description}
              onDragStart={onDragStart}
            />
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="flex-shrink-0 p-4 bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 border-t border-slate-200 dark:border-slate-700">
        <div className="text-center">
          <p className="text-xs text-slate-500 dark:text-slate-400 mb-1">
            💡 Tip: Click components after dropping to configure
          </p>
          <div className="flex justify-center space-x-1">
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-1 h-1 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
