import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Filter, X, Check } from 'lucide-react';
import apiService from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';
import { LocationFilterState } from '@/types/locationFilter';

interface LocationItem {
  id: string;
  name: string;
}

interface LocationTitles {
  tier1: string;
  tier2: string;
  tier3: string;
  tier4: string;
  tier5: string;
  tier6: string;
}

interface LocationFilterDropdownProps {
  onFilterChange: (filters: LocationFilterState) => void;
  className?: string;
}

const LocationFilterDropdown: React.FC<LocationFilterDropdownProps> = ({
  onFilterChange,
  className = ''
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Location data
  const [locationOne, setLocationOne] = useState<LocationItem[]>([]);
  const [locationTwo, setLocationTwo] = useState<LocationItem[]>([]);
  const [locationThree, setLocationThree] = useState<LocationItem[]>([]);
  const [locationFour, setLocationFour] = useState<LocationItem[]>([]);
  const [locationFive, setLocationFive] = useState<LocationItem[]>([]);
  const [locationSix, setLocationSix] = useState<LocationItem[]>([]);

  // Selected values
  const [selectedLocationOne, setSelectedLocationOne] = useState<string>('');
  const [selectedLocationTwo, setSelectedLocationTwo] = useState<string>('');
  const [selectedLocationThree, setSelectedLocationThree] = useState<string>('');
  const [selectedLocationFour, setSelectedLocationFour] = useState<string>('');
  const [selectedLocationFive, setSelectedLocationFive] = useState<string>('');
  const [selectedLocationSix, setSelectedLocationSix] = useState<string>('');

  // Dynamic titles
  const [titles, setTitles] = useState<LocationTitles>({
    tier1: 'Country',
    tier2: 'Region',
    tier3: 'Site',
    tier4: 'Area',
    tier5: 'Unit',
    tier6: 'Sub-Unit'
  });

  // Fetch dynamic titles
  const fetchDynamicTitles = async () => {
    try {
      const response = await apiService.get('/dynamic-titles');
      if (response && response.length > 0) {
        const dynamicTitles = response.reduce((acc: any, item: any) => {
          acc[`tier${item.tier}`] = item.title;
          return acc;
        }, {});
        setTitles(prev => ({ ...prev, ...dynamicTitles }));
      }
    } catch (error) {
      console.error('Error fetching dynamic titles:', error);
    }
  };

  // Fetch location data
  const fetchLocationOne = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.get('/location-ones');
      setLocationOne(response || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch locations",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchLocationTwo = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-ones/${parentId}/location-twos`);
      setLocationTwo(response || []);
    } catch (error) {
      console.error('Error fetching location two:', error);
      setLocationTwo([]);
    }
  };

  const fetchLocationThree = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-twos/${parentId}/location-threes`);
      setLocationThree(response || []);
    } catch (error) {
      console.error('Error fetching location three:', error);
      setLocationThree([]);
    }
  };

  const fetchLocationFour = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-threes/${parentId}/location-fours`);
      setLocationFour(response || []);
    } catch (error) {
      console.error('Error fetching location four:', error);
      setLocationFour([]);
    }
  };

  const fetchLocationFive = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-fours/${parentId}/location-fives`);
      setLocationFive(response || []);
    } catch (error) {
      console.error('Error fetching location five:', error);
      setLocationFive([]);
    }
  };

  const fetchLocationSix = async (parentId: string) => {
    try {
      const response = await apiService.get(`/location-fives/${parentId}/location-sixes`);
      setLocationSix(response || []);
    } catch (error) {
      console.error('Error fetching location six:', error);
      setLocationSix([]);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchDynamicTitles();
    fetchLocationOne();
  }, []);

  // Handle location changes
  const handleLocationOneChange = (value: string) => {
    setSelectedLocationOne(value);
    setSelectedLocationTwo('');
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationTwo([]);
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationTwo(value);
    }
  };

  const handleLocationTwoChange = (value: string) => {
    setSelectedLocationTwo(value);
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationThree(value);
    }
  };

  const handleLocationThreeChange = (value: string) => {
    setSelectedLocationThree(value);
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationFour(value);
    }
  };

  const handleLocationFourChange = (value: string) => {
    setSelectedLocationFour(value);
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationFive([]);
    setLocationSix([]);
    
    if (value) {
      fetchLocationFive(value);
    }
  };

  const handleLocationFiveChange = (value: string) => {
    setSelectedLocationFive(value);
    setSelectedLocationSix('');
    setLocationSix([]);
    
    if (value) {
      fetchLocationSix(value);
    }
  };

  const handleLocationSixChange = (value: string) => {
    setSelectedLocationSix(value);
  };

  // Apply filters
  const handleApplyFilter = () => {
    const filters: LocationFilterState = {
      locationOne: selectedLocationOne,
      locationTwo: selectedLocationTwo,
      locationThree: selectedLocationThree,
      locationFour: selectedLocationFour,
      locationFive: selectedLocationFive,
      locationSix: selectedLocationSix,
    };
    onFilterChange(filters);
    setIsOpen(false);
  };

  // Clear filters
  const handleClearFilter = () => {
    setSelectedLocationOne('');
    setSelectedLocationTwo('');
    setSelectedLocationThree('');
    setSelectedLocationFour('');
    setSelectedLocationFive('');
    setSelectedLocationSix('');
    setLocationTwo([]);
    setLocationThree([]);
    setLocationFour([]);
    setLocationFive([]);
    setLocationSix([]);
    
    const filters: LocationFilterState = {
      locationOne: '',
      locationTwo: '',
      locationThree: '',
      locationFour: '',
      locationFive: '',
      locationSix: '',
    };
    onFilterChange(filters);
    setIsOpen(false);
  };

  // Check if any filter is applied
  const hasActiveFilters = selectedLocationOne || selectedLocationTwo || selectedLocationThree ||
                          selectedLocationFour || selectedLocationFive || selectedLocationSix;

  // Get selected location names for display
  const getSelectedLocationNames = () => {
    const names = [];
    if (selectedLocationOne) {
      const location = locationOne.find(l => l.id === selectedLocationOne);
      if (location) names.push(location.name);
    }
    if (selectedLocationTwo) {
      const location = locationTwo.find(l => l.id === selectedLocationTwo);
      if (location) names.push(location.name);
    }
    if (selectedLocationThree) {
      const location = locationThree.find(l => l.id === selectedLocationThree);
      if (location) names.push(location.name);
    }
    if (selectedLocationFour) {
      const location = locationFour.find(l => l.id === selectedLocationFour);
      if (location) names.push(location.name);
    }
    if (selectedLocationFive) {
      const location = locationFive.find(l => l.id === selectedLocationFive);
      if (location) names.push(location.name);
    }
    if (selectedLocationSix) {
      const location = locationSix.find(l => l.id === selectedLocationSix);
      if (location) names.push(location.name);
    }
    return names;
  };

  const selectedLocationNames = getSelectedLocationNames();
  const displayText = 'Location Filter';

  return (
    <div className="flex items-center justify-start space-x-4">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`relative ${hasActiveFilters ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100' : ''} ${className}`}
            title={hasActiveFilters ? selectedLocationNames.join(' > ') : 'Click to set location filter'}
          >
            <Filter className="h-4 w-4 mr-2" />
            {displayText}
            {hasActiveFilters && (
              <div className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full" />
            )}
          </Button>
        </DropdownMenuTrigger>
      <DropdownMenuContent className="w-96 p-4 shadow-lg border" align="start">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Location Filter</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>



          <div className="grid grid-cols-1 gap-4 max-h-96 overflow-y-auto">
            {/* Location One */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{titles.tier1}</label>
              <Select value={selectedLocationOne} onValueChange={handleLocationOneChange}>
                <SelectTrigger>
                  <SelectValue placeholder={`Select ${titles.tier1}`} />
                </SelectTrigger>
                <SelectContent>
                  {locationOne.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Location Two */}
            {selectedLocationOne && locationTwo.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier2}</label>
                <Select value={selectedLocationTwo} onValueChange={handleLocationTwoChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${titles.tier2}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {locationTwo.map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Location Three */}
            {selectedLocationTwo && locationThree.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier3}</label>
                <Select value={selectedLocationThree} onValueChange={handleLocationThreeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${titles.tier3}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {locationThree.map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Location Four */}
            {selectedLocationThree && locationFour.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier4}</label>
                <Select value={selectedLocationFour} onValueChange={handleLocationFourChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${titles.tier4}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {locationFour.map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Location Five */}
            {selectedLocationFour && locationFive.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier5}</label>
                <Select value={selectedLocationFive} onValueChange={handleLocationFiveChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${titles.tier5}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {locationFive.map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Location Six */}
            {selectedLocationFive && locationSix.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">{titles.tier6}</label>
                <Select value={selectedLocationSix} onValueChange={handleLocationSixChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${titles.tier6}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {locationSix.map((location) => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" size="sm" onClick={handleClearFilter}>
              Clear All
            </Button>
            <Button size="sm" onClick={handleApplyFilter}>
              <Check className="h-4 w-4 mr-2" />
              Apply Filter
            </Button>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>

    {/* Selected Location Path Display */}
    {hasActiveFilters && (
      <div className="flex items-center space-x-2 text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg border">
        <span className="text-xs font-medium text-gray-500">Selected:</span>
        {selectedLocationNames.map((name, index) => (
          <React.Fragment key={index}>
            <span className="font-medium text-gray-800">{name}</span>
            {index < selectedLocationNames.length - 1 && (
              <span className="text-gray-400">&gt;</span>
            )}
          </React.Fragment>
        ))}
      </div>
    )}
    </div>
  );
};

export default LocationFilterDropdown;
