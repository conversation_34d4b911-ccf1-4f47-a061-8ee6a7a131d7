import apiService from './apiService';
import {
  Asset,
  AssetFilter,
  MaintenanceRecord,
  MaintenanceFilter,
  IoTSensor,
  SensorReading,
  SensorAlert,
  CalibrationRecord,
  CalibrationFilter,
  DowntimeRecord,
  ATMDashboard,
  ATMApiResponse,
  AssetCategory
} from '../types/atm';

const ATM_BASE_URL = '/atm';

// Asset Management APIs
export const fetchAssets = async (filter?: AssetFilter): Promise<Asset[]> => {
  try {
    const params = filter ? `?filter=${encodeURIComponent(JSON.stringify(filter))}` : '';
    return await apiService.get(`${ATM_BASE_URL}/assets${params}`);
  } catch (error) {
    console.error('Error fetching assets:', error);
    throw error;
  }
};

export const fetchAssetById = async (assetId: string): Promise<Asset> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/assets/${assetId}`);
  } catch (error) {
    console.error('Error fetching asset:', error);
    throw error;
  }
};

export const createAsset = async (assetData: Partial<Asset>): Promise<Asset> => {
  try {
    return await apiService.post(`${ATM_BASE_URL}/assets`, assetData);
  } catch (error) {
    console.error('Error creating asset:', error);
    throw error;
  }
};

export const updateAsset = async (assetId: string, assetData: Partial<Asset>): Promise<Asset> => {
  try {
    return await apiService.patch(`${ATM_BASE_URL}/assets/${assetId}`, assetData);
  } catch (error) {
    console.error('Error updating asset:', error);
    throw error;
  }
};

export const deleteAsset = async (assetId: string): Promise<void> => {
  try {
    await apiService.delete(`${ATM_BASE_URL}/assets/${assetId}`);
  } catch (error) {
    console.error('Error deleting asset:', error);
    throw error;
  }
};

export const fetchAssetCategories = async (): Promise<AssetCategory[]> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/asset-categories`);
  } catch (error) {
    console.error('Error fetching asset categories:', error);
    throw error;
  }
};

// Maintenance Management APIs
export const fetchMaintenanceRecords = async (filter?: MaintenanceFilter): Promise<MaintenanceRecord[]> => {
  try {
    const params = filter ? `?filter=${encodeURIComponent(JSON.stringify(filter))}` : '';
    return await apiService.get(`${ATM_BASE_URL}/maintenance${params}`);
  } catch (error) {
    console.error('Error fetching maintenance records:', error);
    throw error;
  }
};

export const fetchMaintenanceById = async (maintenanceId: string): Promise<MaintenanceRecord> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/maintenance/${maintenanceId}`);
  } catch (error) {
    console.error('Error fetching maintenance record:', error);
    throw error;
  }
};

export const createMaintenanceRecord = async (maintenanceData: Partial<MaintenanceRecord>): Promise<MaintenanceRecord> => {
  try {
    return await apiService.post(`${ATM_BASE_URL}/maintenance`, maintenanceData);
  } catch (error) {
    console.error('Error creating maintenance record:', error);
    throw error;
  }
};

export const updateMaintenanceRecord = async (maintenanceId: string, maintenanceData: Partial<MaintenanceRecord>): Promise<MaintenanceRecord> => {
  try {
    return await apiService.patch(`${ATM_BASE_URL}/maintenance/${maintenanceId}`, maintenanceData);
  } catch (error) {
    console.error('Error updating maintenance record:', error);
    throw error;
  }
};

export const deleteMaintenanceRecord = async (maintenanceId: string): Promise<void> => {
  try {
    await apiService.delete(`${ATM_BASE_URL}/maintenance/${maintenanceId}`);
  } catch (error) {
    console.error('Error deleting maintenance record:', error);
    throw error;
  }
};

// IoT and Sensor APIs
export const fetchSensors = async (assetId?: string): Promise<IoTSensor[]> => {
  try {
    const params = assetId ? `?assetId=${assetId}` : '';
    return await apiService.get(`${ATM_BASE_URL}/sensors${params}`);
  } catch (error) {
    console.error('Error fetching sensors:', error);
    throw error;
  }
};

export const fetchSensorById = async (sensorId: string): Promise<IoTSensor> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/sensors/${sensorId}`);
  } catch (error) {
    console.error('Error fetching sensor:', error);
    throw error;
  }
};

export const fetchSensorReadings = async (sensorId: string, startTime?: string, endTime?: string): Promise<SensorReading[]> => {
  try {
    const params = new URLSearchParams();
    if (startTime) params.append('startTime', startTime);
    if (endTime) params.append('endTime', endTime);
    const queryString = params.toString() ? `?${params.toString()}` : '';
    
    return await apiService.get(`${ATM_BASE_URL}/sensors/${sensorId}/readings${queryString}`);
  } catch (error) {
    console.error('Error fetching sensor readings:', error);
    throw error;
  }
};

export const fetchSensorAlerts = async (acknowledged?: boolean): Promise<SensorAlert[]> => {
  try {
    const params = acknowledged !== undefined ? `?acknowledged=${acknowledged}` : '';
    return await apiService.get(`${ATM_BASE_URL}/sensor-alerts${params}`);
  } catch (error) {
    console.error('Error fetching sensor alerts:', error);
    throw error;
  }
};

export const acknowledgeSensorAlert = async (alertId: string): Promise<void> => {
  try {
    await apiService.patch(`${ATM_BASE_URL}/sensor-alerts/${alertId}/acknowledge`);
  } catch (error) {
    console.error('Error acknowledging sensor alert:', error);
    throw error;
  }
};

// Calibration Management APIs
export const fetchCalibrationRecords = async (filter?: CalibrationFilter): Promise<CalibrationRecord[]> => {
  try {
    const params = filter ? `?filter=${encodeURIComponent(JSON.stringify(filter))}` : '';
    return await apiService.get(`${ATM_BASE_URL}/calibrations${params}`);
  } catch (error) {
    console.error('Error fetching calibration records:', error);
    throw error;
  }
};

export const fetchCalibrationById = async (calibrationId: string): Promise<CalibrationRecord> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/calibrations/${calibrationId}`);
  } catch (error) {
    console.error('Error fetching calibration record:', error);
    throw error;
  }
};

export const createCalibrationRecord = async (calibrationData: Partial<CalibrationRecord>): Promise<CalibrationRecord> => {
  try {
    return await apiService.post(`${ATM_BASE_URL}/calibrations`, calibrationData);
  } catch (error) {
    console.error('Error creating calibration record:', error);
    throw error;
  }
};

export const updateCalibrationRecord = async (calibrationId: string, calibrationData: Partial<CalibrationRecord>): Promise<CalibrationRecord> => {
  try {
    return await apiService.patch(`${ATM_BASE_URL}/calibrations/${calibrationId}`, calibrationData);
  } catch (error) {
    console.error('Error updating calibration record:', error);
    throw error;
  }
};

// Downtime Management APIs
export const fetchDowntimeRecords = async (assetId?: string): Promise<DowntimeRecord[]> => {
  try {
    const params = assetId ? `?assetId=${assetId}` : '';
    return await apiService.get(`${ATM_BASE_URL}/downtime${params}`);
  } catch (error) {
    console.error('Error fetching downtime records:', error);
    throw error;
  }
};

export const createDowntimeRecord = async (downtimeData: Partial<DowntimeRecord>): Promise<DowntimeRecord> => {
  try {
    return await apiService.post(`${ATM_BASE_URL}/downtime`, downtimeData);
  } catch (error) {
    console.error('Error creating downtime record:', error);
    throw error;
  }
};

export const updateDowntimeRecord = async (downtimeId: string, downtimeData: Partial<DowntimeRecord>): Promise<DowntimeRecord> => {
  try {
    return await apiService.patch(`${ATM_BASE_URL}/downtime/${downtimeId}`, downtimeData);
  } catch (error) {
    console.error('Error updating downtime record:', error);
    throw error;
  }
};

// Dashboard and Analytics APIs
export const fetchATMDashboard = async (): Promise<ATMDashboard> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/dashboard`);
  } catch (error) {
    console.error('Error fetching ATM dashboard:', error);
    throw error;
  }
};

export const fetchAssetUtilization = async (assetId: string, period: string): Promise<any> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/assets/${assetId}/utilization?period=${period}`);
  } catch (error) {
    console.error('Error fetching asset utilization:', error);
    throw error;
  }
};

export const fetchMaintenanceTrends = async (period: string): Promise<any> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/analytics/maintenance-trends?period=${period}`);
  } catch (error) {
    console.error('Error fetching maintenance trends:', error);
    throw error;
  }
};

export const fetchDowntimeAnalytics = async (period: string): Promise<any> => {
  try {
    return await apiService.get(`${ATM_BASE_URL}/analytics/downtime?period=${period}`);
  } catch (error) {
    console.error('Error fetching downtime analytics:', error);
    throw error;
  }
};

// File Upload for ATM
export const uploadATMFile = async (file: File, type: 'asset' | 'maintenance' | 'calibration'): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    const response = await apiService.post(`${ATM_BASE_URL}/files/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    
    return response.filename;
  } catch (error) {
    console.error('Error uploading ATM file:', error);
    throw error;
  }
};
