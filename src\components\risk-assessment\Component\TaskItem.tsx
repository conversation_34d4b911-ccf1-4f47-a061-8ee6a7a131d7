import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { GripVertical, Trash2, CheckCircle, Clock } from 'lucide-react';

interface TaskStatus {
  hazardsIdentification: string;
  consequences: string;
  currentControls: string;
  riskEstimation: string;
  additionalControls: string;
}

interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: any[];
  option?: any[];
  severity?: string | number;
  likelyhood?: string | number;
  level?: string | string[] | any;
  accept?: boolean;
  step?: number;
  value?: TaskStatus | string[] | number | any;
}

interface TableData {
  id: string;
  severity: string;
  rare: string;
  unlikely: string;
  possible: string;
  likely: string;
  almostCertain: string;
}

interface TaskItemProps {
  item: TaskItem[];
  index: number;
  openDialog: (item: TaskItem[], index: number) => void;
  subActivity?: any;
  deleteTask: (e: React.MouseEvent, index: number) => void;
  onDragStart: (e: React.DragEvent, index: number) => void;
  onDrop: (e: React.DragEvent, index: number) => void;
  onDragOver: (e: React.DragEvent) => void;
  type?: string;
  cellClassName: (value: number) => string;
  tableData?: TableData[];
}

const TaskItem: React.FC<TaskItemProps> = ({
  item,
  index,
  openDialog,
  subActivity,
  deleteTask,
  onDragStart,
  onDrop,
  onDragOver,
  type,
  cellClassName,
  tableData = []
}) => {
  const severity = Number(item[4]?.severity) || 0;
  const likelihood = Number(item[4]?.likelyhood) || 0;
  const riskValue = severity * likelihood;

  // Function to find the risk level from the matrix table
  const findMatrixValue = (idValue: string | number, columnValue: string | number): string => {
    const columnMap: Record<number, keyof TableData> = {
      1: 'rare',
      2: 'unlikely',
      3: 'possible',
      4: 'likely',
      5: 'almostCertain'
    };

    const columnKey = columnMap[Number(columnValue)];
    const row = tableData.find(item => item.id.startsWith(`${idValue}(`));

    if (row && row[columnKey]) {
      return row[columnKey] as string;
    }

    return '0';
  };

  // Get the risk level description from the matrix
  const getRiskLevel = () => {
    if (severity > 0 && likelihood > 0 && tableData.length > 0) {
      return findMatrixValue(severity, likelihood);
    }
    return 'Not Assessed';
  };

  const riskLevel = getRiskLevel();

  return (
    <div
      className="group mb-4"
      onDrop={(e) => onDrop(e, index)}
      onDragOver={(e) => onDragOver(e)}
    >
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
        {/* Header with number and title */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Numbered Circle */}
              <div className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full font-bold text-lg shadow-sm">
                {index + 1}
              </div>

              {/* Drag handle */}
              {!type && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="cursor-move p-2 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  draggable
                  onDragStart={(e) => onDragStart(e, index)}
                >
                  <GripVertical className="h-4 w-4" />
                </Button>
              )}

              {/* Title */}
              <h6
                className="text-lg font-semibold cursor-pointer text-blue-600 hover:text-blue-800 underline underline-offset-2 transition-all duration-200"
                onClick={() => openDialog(item, index)}
                title={item[0]?.name}
              >
                {item[0]?.name && item[0].name.length > 50
                  ? `${item[0].name.slice(0, 50)}...`
                  : item[0]?.name}
              </h6>
            </div>

            {/* Delete Icon */}
            {!type && (
              <Button
                variant="ghost"
                size="sm"
                className="w-10 h-10 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-all"
                onClick={(e) => deleteTask(e, index)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Content with stages */}
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-600">Progress:</span>
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${cellClassName(riskValue)}`}>
                Risk Level: {riskLevel}
              </div>
            </div>

            {/* Stages */}
            <div className="flex items-center gap-2 flex-wrap">
              {item[9]?.level?.map((level: string, l: number) => {
                // Map the stage in item[9].level to its corresponding key in item[11].value
                const statusMap: Record<string, keyof TaskStatus> = {
                  "Hazards Identification": "hazardsIdentification",
                  "Consequences": "consequences",
                  "Current Controls": "currentControls",
                  "Risk Estimation": "riskEstimation",
                  "Additional Controls": "additionalControls"
                };

                // Get the status for the current level
                const statusKey = statusMap[level];
                const stageStatus = item[11]?.value?.[statusKey];

                // Determine the badge variant and styling
                const getBadgeClass = () => {
                  if (stageStatus === "completed") return "bg-green-100 text-green-700 border-green-200";
                  if (stageStatus === "inprogress") return "bg-blue-100 text-blue-700 border-blue-200";
                  return "bg-gray-100 text-gray-600 border-gray-200";
                };

                const getIcon = () => {
                  if (stageStatus === "completed") return <CheckCircle className="h-3 w-3" />;
                  if (stageStatus === "inprogress") return <Clock className="h-3 w-3" />;
                  return null;
                };

                return (
                  <div
                    key={l}
                    className={`flex items-center gap-1 px-2 py-1 rounded-md border text-xs font-medium ${getBadgeClass()}`}
                  >
                    {getIcon()}
                    <span className="hidden sm:inline">{level}</span>
                    <span className="sm:hidden">{level.split(' ')[0]}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskItem;
