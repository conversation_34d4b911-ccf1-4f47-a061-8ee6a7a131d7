import React from "react";
import { ContentMode } from "@/types/curate";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, FileText } from "lucide-react";

interface ModeToggleProps {
  activeMode: ContentMode;
  onChange: (mode: ContentMode) => void;
}

const ModeToggle: React.FC<ModeToggleProps> = ({ activeMode, onChange }) => {
  return (
    <Tabs
      value={activeMode}
      onValueChange={(value) => onChange(value as ContentMode)}
      className="w-full"
    >
      <TabsList className="w-full">
        <TabsTrigger value="communicate" className="flex-1 flex items-center justify-center gap-2">
          <MessageSquare className="h-4 w-4" />
          <span>Content</span>
        </TabsTrigger>
        <TabsTrigger value="feedback" className="flex-1 flex items-center justify-center gap-2">
          <FileText className="h-4 w-4" />
          <span>Form</span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default ModeToggle;
