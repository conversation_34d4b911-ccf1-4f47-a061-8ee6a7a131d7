import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import {
  Plus,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Gauge,
  FileText,
  Download
} from 'lucide-react';
import { CalibrationRecord, CalibrationStatus, CalibrationFrequency } from '@/types/atm';

interface CalibrationManagementProps {
  searchQuery: string;
}

const CalibrationManagement = ({ searchQuery }: CalibrationManagementProps) => {
  const [calibrationRecords, setCalibrationRecords] = useState<CalibrationRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadCalibrationRecords();
  }, []);

  const loadCalibrationRecords = async () => {
    try {
      setIsLoading(true);
      // Mock data for demo
      setCalibrationRecords(getMockCalibrationRecords());
      toast({
        title: "Demo Mode",
        description: "Using mock calibration data for demonstration",
        variant: "default"
      });
    } catch (error) {
      console.error('Error loading calibration records:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMockCalibrationRecords = (): CalibrationRecord[] => [
    {
      id: '1',
      assetId: '1',
      title: 'HVAC Pressure Sensor Calibration',
      description: 'Annual calibration of pressure monitoring sensors',
      calibrationDate: '2024-01-15T09:00:00Z',
      nextCalibrationDate: '2024-07-15T09:00:00Z',
      frequency: CalibrationFrequency.SEMI_ANNUAL,
      status: CalibrationStatus.COMPLETED,
      performedBy: 'calibration_tech1',
      certificateNumber: 'CAL-2024-001',
      standardUsed: 'NIST Traceable Pressure Standard',
      results: [
        { parameter: 'Pressure', expectedValue: 50, actualValue: 49.8, tolerance: 1, unit: 'PSI', passed: true }
      ],
      notes: 'Calibration within acceptable limits',
      attachments: ['calibration_certificate_001.pdf'],
      cost: 350,
      created: '2024-01-10T10:00:00Z',
      updated: '2024-01-15T16:00:00Z'
    },
    {
      id: '2',
      assetId: '2',
      title: 'Temperature Sensor Calibration',
      description: 'Quarterly calibration of temperature monitoring',
      calibrationDate: '2024-02-01T08:00:00Z',
      nextCalibrationDate: '2024-05-01T08:00:00Z',
      frequency: CalibrationFrequency.QUARTERLY,
      status: CalibrationStatus.DUE,
      performedBy: 'calibration_tech2',
      standardUsed: 'RTD Reference Standard',
      results: [],
      notes: 'Due for calibration',
      attachments: [],
      created: '2024-01-20T09:00:00Z',
      updated: '2024-01-20T09:00:00Z'
    }
  ];

  const getStatusBadge = (status: CalibrationStatus) => {
    const variants = {
      [CalibrationStatus.DUE]: 'destructive',
      [CalibrationStatus.OVERDUE]: 'destructive',
      [CalibrationStatus.COMPLETED]: 'secondary',
      [CalibrationStatus.IN_PROGRESS]: 'default',
      [CalibrationStatus.CANCELLED]: 'outline'
    };
    return <Badge variant={variants[status] as any}>{status}</Badge>;
  };

  const columns = [
    {
      key: 'title',
      label: 'Calibration Task',
      sortable: true,
      render: (record: CalibrationRecord) => (
        <div>
          <div className="font-medium">{record.title}</div>
          <div className="text-sm text-muted-foreground">{record.description}</div>
        </div>
      )
    },
    {
      key: 'nextCalibrationDate',
      label: 'Next Due Date',
      sortable: true,
      render: (record: CalibrationRecord) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{new Date(record.nextCalibrationDate).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'frequency',
      label: 'Frequency',
      sortable: true,
      render: (record: CalibrationRecord) => (
        <Badge variant="outline">{record.frequency}</Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (record: CalibrationRecord) => getStatusBadge(record.status)
    }
  ];

  const dueCalibrations = calibrationRecords.filter(record => 
    record.status === CalibrationStatus.DUE || record.status === CalibrationStatus.OVERDUE
  );

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Schedule
          </Button>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Schedule Calibration
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due Soon</p>
                <p className="text-2xl font-bold text-orange-600">{dueCalibrations.length}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-600">
                  {calibrationRecords.filter(r => r.status === CalibrationStatus.OVERDUE).length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed (30 days)</p>
                <p className="text-2xl font-bold">
                  {calibrationRecords.filter(r => r.status === CalibrationStatus.COMPLETED).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Instruments</p>
                <p className="text-2xl font-bold">{calibrationRecords.length}</p>
              </div>
              <Gauge className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calibration Records Table */}
      <Card>
        <CardHeader>
          <CardTitle>Calibration Schedule</CardTitle>
          <CardDescription>
            {calibrationRecords.length} calibration records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExpandableDataTable
            data={calibrationRecords}
            columns={columns}
            loading={isLoading}
            searchable={false}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default CalibrationManagement;
