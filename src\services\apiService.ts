import API from './axiosAPI';
import { AxiosResponse } from 'axios';

export interface FileUploadResponse {
  files: {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
  }[];
  fields: Record<string, unknown>;
}

/**
 * Centralized API service that uses the axios instance with 401 handling
 * This ensures all API calls go through the same interceptors
 */
class ApiService {
  /**
   * Generic GET request
   */
  async get<T = any>(url: string, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.get(url, config);
    return response.data;
  }

  /**
   * Generic POST request
   */
  async post<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.post(url, data, config);
    return response.data;
  }

  /**
   * Generic PUT request
   */
  async put<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.put(url, data, config);
    return response.data;
  }

  /**
   * Generic PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.patch(url, data, config);
    return response.data;
  }

  /**
   * Generic DELETE request
   */
  async delete<T = any>(url: string, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.delete(url, config);
    return response.data;
  }

  /**
   * Upload file with FormData
   */
  async uploadFile<T = any>(url: string, formData: FormData): Promise<T> {
    const response: AxiosResponse<T> = await API.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Download file as blob
   */
  async downloadBlob(url: string): Promise<Blob> {
    const response: AxiosResponse<Blob> = await API.get(url, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Get request that returns the full axios response (for cases where you need headers, status, etc.)
   */
  async getResponse<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return await API.get(url, config);
  }

  /**
   * Post request that returns the full axios response
   */
  async postResponse<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return await API.post(url, data, config);
  }

  /**
   * Upload file to /files endpoint
   */
  async uploadFileToFiles(file: File): Promise<FileUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.post<FileUploadResponse>('/files', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('File uploaded successfully:', response);
      return response;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * Get file download URL (presigned URL)
   */
  async getFileDownloadUrl(fileName: string): Promise<string> {
    try {
      const response = await this.get<{ url: string }>(`/files/${fileName}/presigned-url`);
      return response.url;
    } catch (error) {
      console.error('Error getting file download URL:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const apiService = new ApiService();
export default apiService;
