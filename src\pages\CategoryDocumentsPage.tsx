import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, FileText, Search, Loader2, Calendar, User, Tag, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import DocumentPDFViewer from '@/components/DocumentPDFViewer';

// API Configuration
const MY_DOCUMENTS_API = `${API_BASE_URL}/my-documents`;
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;

interface Document {
  id: string;
  name: string;
  type: string;
  size?: string;
  uploadedBy?: string;
  uploadedDate?: string;
  category?: string;
  tags?: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: any;
  creator?: any;
  reviewer?: any;
  approver?: any;
  documentCategory?: any;
  files?: any;
  status?: string;
  docStatus?: string;
  value?: any;
}

interface CategoryOption {
  label: string;
  value: string;
}

const CategoryDocumentsPage: React.FC = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // State management
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [categoryInfo, setCategoryInfo] = useState<CategoryOption | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  // Fetch category information
  const fetchCategoryInfo = useCallback(async () => {
    try {
      const maskId = 'doc_category';
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const categories = response[0]?.dropdownItems.map((item: { name: string; id: string }) => ({
        label: item.name,
        value: item.id,
      })) || [];

      const category = categories.find((cat: CategoryOption) => cat.value === categoryId);
      setCategoryInfo(category || null);
    } catch (error) {
      console.error('Error fetching category info:', error);
    }
  }, [categoryId]);

  // Fetch documents for this category
  const fetchCategoryDocuments = useCallback(async () => {
    if (!categoryId) return;

    try {
      setLoading(true);
      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const url = `${MY_DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);

      console.log('My documents response:', response);

      // Transform and filter documents for this category
      const transformedDocuments = response.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        size: doc.size || 'Unknown',
        uploadedBy: doc.creator?.firstName || doc.uploadedBy || doc.createdBy || 'Not assigned',
        uploadedDate: doc.uploadedDate || doc.created || new Date().toISOString(),
        category: doc.documentCategory?.name || doc.category || 'Uncategorized',
        tags: doc.keywords ? doc.keywords.split(',').map((tag: string) => tag.trim()) : [],
        description: doc.purpose || doc.description || '',
        maskId: doc.maskId,
        scopeApplicability: doc.scopeApplicability,
        purpose: doc.purpose,
        keywords: doc.keywords,
        docId: doc.docId,
        created: doc.created,
        updated: doc.updated,
        creatorTargetDate: doc.creatorTargetDate,
        reviewerTargetDate: doc.reviewerTargetDate,
        approverTargetDate: doc.approverTargetDate,
        initiatorId: doc.initiatorId,
        creatorId: doc.creatorId,
        reviewerId: doc.reviewerId,
        approverId: doc.approverId,
        documentCategoryId: doc.documentCategoryId,
        initiator: doc.initiator,
        creator: doc.creator,
        reviewer: doc.reviewer,
        approver: doc.approver,
        documentCategory: doc.documentCategory,
        files: doc.files,
        status: doc.status || doc.docStatus || 'Draft',
        docStatus: doc.docStatus,
        value: doc.value
      }));

      // Filter documents for this specific category
      const categoryDocuments = transformedDocuments.filter((doc: Document) => 
        doc.documentCategoryId === categoryId
      );

      console.log('Category documents:', categoryDocuments);
      setDocuments(categoryDocuments);
      setFilteredDocuments(categoryDocuments);

      // Auto-select first document if available
      if (categoryDocuments.length > 0) {
        setSelectedDocument(categoryDocuments[0]);
      }

    } catch (error) {
      console.error('Error fetching category documents:', error);
      toast({
        title: "Error",
        description: "Failed to load documents. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [categoryId, toast]);

  // Filter documents based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDocuments(documents);
    } else {
      const filtered = documents.filter(doc =>
        doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.maskId?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.creator?.firstName?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDocuments(filtered);
    }
  }, [searchQuery, documents]);

  // Load data on component mount
  useEffect(() => {
    fetchCategoryInfo();
    fetchCategoryDocuments();
  }, [fetchCategoryInfo, fetchCategoryDocuments]);

  // Handle document selection
  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'under review':
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'pending':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/apps/doc')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Documents
            </Button>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {categoryInfo?.label || 'Category Documents'}
                </h1>
                <p className="text-sm text-gray-600">
                  {filteredDocuments.length} document{filteredDocuments.length !== 1 ? 's' : ''} available
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Sidebar - Document List */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Search */}
          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Document List */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                  <p className="text-muted-foreground">Loading documents...</p>
                </div>
              </div>
            ) : filteredDocuments.length > 0 ? (
              <div className="space-y-1 p-2">
                {filteredDocuments.map((document) => (
                  <div
                    key={document.id}
                    onClick={() => handleDocumentSelect(document)}
                    className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                      selectedDocument?.id === document.id
                        ? 'bg-blue-50 border-blue-300 shadow-sm'
                        : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                    }`}
                  >
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className={`font-medium text-sm leading-tight ${
                          selectedDocument?.id === document.id ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {document.name}
                        </h3>
                        <div className={`inline-flex items-center px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(document.status || 'Draft')}`}>
                          {document.status || 'Draft'}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span className="font-medium">{document.maskId || document.docId}</span>
                        {document.created && (
                          <>
                            <span>•</span>
                            <Calendar className="h-3 w-3" />
                            <span>{format(new Date(document.created), 'MMM dd, yyyy')}</span>
                          </>
                        )}
                      </div>
                      
                      {document.creator?.firstName && (
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <User className="h-3 w-3" />
                          <span>{document.creator.firstName}</span>
                        </div>
                      )}
                      
                      {document.description && (
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {document.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center p-6">
                <div className="p-4 bg-gray-100 rounded-full mb-4">
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Documents Found</h3>
                <p className="text-gray-600 max-w-md">
                  {searchQuery ? 'No documents match your search criteria.' : 'No documents are available in this category yet.'}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Document PDF View */}
        <div className="flex-1 bg-gray-100 overflow-y-auto">
          {selectedDocument ? (
            <DocumentPDFViewer document={selectedDocument} />
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center p-6 bg-white mx-6 my-6 rounded-lg shadow-sm">
              <div className="p-4 bg-gray-100 rounded-full mb-4">
                <Eye className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Document</h3>
              <p className="text-gray-600 max-w-md">
                Choose a document from the sidebar to view it in PDF-like format.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryDocumentsPage;
