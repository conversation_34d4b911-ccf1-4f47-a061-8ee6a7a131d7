import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  Upload, 
  X, 
  Image, 
  Video, 
  FileText, 
  Camera, 
  VideoIcon,
  Paperclip
} from 'lucide-react';
import { AttachmentConfig } from '@/types/draggable';
import API from '@/services/axiosAPI';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { API_BASE_URL } from '@/constants/index';

const FILE_URL = `${API_BASE_URL}/files`;

interface AttachmentUploadComponentProps {
  attachmentConfig: AttachmentConfig;
  onFilesUpload: (fileNames: string[]) => void;
  initialFiles?: string[];
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  description?: string;
}

const AttachmentUploadComponent: React.FC<AttachmentUploadComponentProps> = ({
  attachmentConfig,
  onFilesUpload,
  initialFiles = [],
  maxFiles = 10,
  maxFileSize = 52428800, // 50MB default
  description = "Upload files"
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<string[]>(initialFiles);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { accessToken } = useSelector((state: RootState) => state.auth);

  // Get enabled attachment types
  const enabledTypes = {
    image: attachmentConfig.image.enabled,
    video: attachmentConfig.video.enabled,
    documents: attachmentConfig.documents.enabled
  };

  // Debug log component initialization
  console.log('AttachmentUpload: Component initialized', {
    attachmentConfig,
    enabledTypes,
    initialFiles,
    maxFiles,
    maxFileSize
  });

  // Build accept string based on enabled types
  const getAcceptString = () => {
    const acceptTypes: string[] = [];
    
    if (enabledTypes.image) {
      acceptTypes.push('image/*');
    }
    if (enabledTypes.video) {
      acceptTypes.push('video/*');
    }
    if (enabledTypes.documents) {
      acceptTypes.push('.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt');
    }
    
    return acceptTypes.length > 0 ? acceptTypes.join(',') : '*/*';
  };

  // Get file type category
  const getFileTypeCategory = (fileName: string) => {
    const extension = fileName.toLowerCase().split('.').pop();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension || '')) {
      return 'image';
    }
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension || '')) {
      return 'video';
    }
    if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'].includes(extension || '')) {
      return 'document';
    }
    return 'other';
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const category = getFileTypeCategory(fileName);
    
    switch (category) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-500" />;
      case 'video':
        return <Video className="h-4 w-4 text-green-500" />;
      case 'document':
        return <FileText className="h-4 w-4 text-red-500" />;
      default:
        return <Paperclip className="h-4 w-4 text-gray-500" />;
    }
  };

  // Validate file type against enabled types
  const isFileTypeAllowed = (fileName: string) => {
    const category = getFileTypeCategory(fileName);
    
    switch (category) {
      case 'image':
        return enabledTypes.image;
      case 'video':
        return enabledTypes.video;
      case 'document':
        return enabledTypes.documents;
      default:
        return false;
    }
  };

  const handleFileUpload = async (files: FileList) => {
    console.log('AttachmentUpload: Starting file upload', {
      filesCount: files.length,
      attachmentConfig,
      enabledTypes
    });

    if (!files || files.length === 0) {
      console.log('AttachmentUpload: No files selected');
      return;
    }

    // Check file count limit
    if (uploadedFiles.length + files.length > maxFiles) {
      console.log('AttachmentUpload: Too many files', {
        current: uploadedFiles.length,
        adding: files.length,
        max: maxFiles
      });
      toast({
        title: "Too many files",
        description: `Maximum ${maxFiles} files allowed`,
        variant: "destructive"
      });
      return;
    }

    setUploading(true);
    const newFileNames: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log('AttachmentUpload: Processing file', {
          name: file.name,
          size: file.size,
          type: file.type
        });

        // Check file size
        if (file.size > maxFileSize) {
          console.log('AttachmentUpload: File too large', {
            name: file.name,
            size: file.size,
            maxSize: maxFileSize
          });
          toast({
            title: "File too large",
            description: `${file.name} exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`,
            variant: "destructive"
          });
          continue;
        }

        // Check file type
        const fileCategory = getFileTypeCategory(file.name);
        const isAllowed = isFileTypeAllowed(file.name);
        console.log('AttachmentUpload: File type check', {
          name: file.name,
          category: fileCategory,
          isAllowed,
          enabledTypes
        });

        if (!isAllowed) {
          const enabledTypesText = Object.entries(enabledTypes)
            .filter(([_, enabled]) => enabled)
            .map(([type, _]) => type)
            .join(', ');

          console.log('AttachmentUpload: File type not allowed', {
            name: file.name,
            category: fileCategory,
            enabledTypes: enabledTypesText
          });

          toast({
            title: "File type not allowed",
            description: `${file.name} is not allowed. Enabled types: ${enabledTypesText}`,
            variant: "destructive"
          });
          continue;
        }

        // Upload file
        console.log('AttachmentUpload: Starting API upload for', file.name);
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await API.post(FILE_URL, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Authorization': `Bearer ${accessToken}`
            },
          });

          console.log('AttachmentUpload: API response', {
            fileName: file.name,
            response,
            status: response?.status,
            data: response?.data
          });

          if (response && response.status === 200) {
            // Extract filename using the same pattern as FileUploadComponent
            const uploadedFileName = response.data.files[0].originalname;

            newFileNames.push(uploadedFileName);
            console.log('AttachmentUpload: File uploaded successfully', {
              originalName: file.name,
              uploadedName: uploadedFileName
            });
          } else {
            console.log('AttachmentUpload: Upload failed - invalid response', {
              fileName: file.name,
              status: response?.status,
              response: response?.data
            });
            toast({
              title: "Upload failed",
              description: `Failed to upload ${file.name} - invalid response`,
              variant: "destructive"
            });
          }
        } catch (error) {
          console.error('AttachmentUpload: File upload error:', error);
          toast({
            title: "Upload Failed",
            description: `Failed to upload ${file.name}. Please try again.`,
            variant: "destructive"
          });
        }
      }

      console.log('AttachmentUpload: Upload process completed', {
        newFileNames,
        totalUploaded: newFileNames.length
      });

      if (newFileNames.length > 0) {
        const updatedFiles = [...uploadedFiles, ...newFileNames];
        console.log('AttachmentUpload: Updating file list', {
          previousFiles: uploadedFiles,
          newFiles: newFileNames,
          updatedFiles
        });

        setUploadedFiles(updatedFiles);
        onFilesUpload(updatedFiles);

        toast({
          title: "Upload successful",
          description: `${newFileNames.length} file(s) uploaded successfully`,
          variant: "default"
        });
      } else {
        console.log('AttachmentUpload: No files were successfully uploaded');
        toast({
          title: "Upload incomplete",
          description: "No files were successfully uploaded. Check console for details.",
          variant: "destructive"
        });
      }
    } finally {
      setUploading(false);
      console.log('AttachmentUpload: Upload process finished');
    }
  };

  const handleFileRemove = (fileName: string) => {
    const updatedFiles = uploadedFiles.filter(f => f !== fileName);
    setUploadedFiles(updatedFiles);
    onFilesUpload(updatedFiles);
  };

  const handleFileInputClick = () => {
    fileInputRef.current?.click();
  };

  // Get enabled types for display
  const getEnabledTypesDisplay = () => {
    const types: string[] = [];
    if (enabledTypes.image) types.push('Images');
    if (enabledTypes.video) types.push('Videos');
    if (enabledTypes.documents) types.push('Documents');
    return types.join(', ');
  };

  // Check if any attachment type is enabled
  const hasEnabledTypes = enabledTypes.image || enabledTypes.video || enabledTypes.documents;

  if (!hasEnabledTypes) {
    return (
      <Card className="border-dashed border-gray-300">
        <CardContent className="p-6 text-center">
          <Paperclip className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500">No attachment types enabled</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card className="border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-6">
          <div className="text-center">
            <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm font-medium mb-1">{description}</p>
            <p className="text-xs text-gray-500 mb-3">
              Allowed: {getEnabledTypesDisplay()} • Max {maxFiles} files • {Math.round(maxFileSize / 1024 / 1024)}MB each
            </p>
            
            <Button 
              onClick={handleFileInputClick}
              disabled={uploading || uploadedFiles.length >= maxFiles}
              variant="outline"
              size="sm"
            >
              {uploading ? 'Uploading...' : 'Choose Files'}
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={getAcceptString()}
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              className="hidden"
            />
          </div>

          {/* Upload Options Display */}
          <div className="mt-4 flex flex-wrap gap-2 justify-center">
            {enabledTypes.image && (
              <Badge variant="outline" className="text-xs">
                <Image className="h-3 w-3 mr-1" />
                Images {attachmentConfig.image.galleryUploads ? '(Gallery)' : '(Device)'}
              </Badge>
            )}
            {enabledTypes.video && (
              <Badge variant="outline" className="text-xs">
                <VideoIcon className="h-3 w-3 mr-1" />
                Videos {attachmentConfig.video.galleryUploads ? '(Gallery)' : '(Device)'}
              </Badge>
            )}
            {enabledTypes.documents && (
              <Badge variant="outline" className="text-xs">
                <FileText className="h-3 w-3 mr-1" />
                Documents (Device)
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium">Uploaded Files ({uploadedFiles.length}/{maxFiles})</p>
          <div className="space-y-2">
            {uploadedFiles.map((fileName, index) => (
              <Card key={index} className="border">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getFileTypeIcon(fileName)}
                      <span className="text-sm font-medium truncate">{fileName}</span>
                      <Badge variant="secondary" className="text-xs">
                        {getFileTypeCategory(fileName)}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFileRemove(fileName)}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AttachmentUploadComponent;
