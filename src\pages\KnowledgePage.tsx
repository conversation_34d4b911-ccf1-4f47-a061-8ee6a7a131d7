import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BookOpen, FileText, Plus, Search, BookMarked, Clock, FolderOpen, Target, Building, Edit, MoreVertical, Trash2, Settings } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import apiService from '@/services/apiService';

// Types for the curate tab
interface KnowledgeArea {
  id: string;
  name: string;
  description?: string;
}

interface Topic {
  id: string;
  name: string;
  areaId: string;
  description?: string;
}

interface Unit {
  id: string;
  name: string;
  topicId: string;
  description?: string;
}

const KnowledgePage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  // Curate tab state
  const [knowledgeAreas, setKnowledgeAreas] = useState<KnowledgeArea[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedArea, setSelectedArea] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Add dialog state
  const [isAddAreaDialogOpen, setIsAddAreaDialogOpen] = useState(false);
  const [isAddTopicDialogOpen, setIsAddTopicDialogOpen] = useState(false);
  const [isAddUnitDialogOpen, setIsAddUnitDialogOpen] = useState(false);
  const [newAreaName, setNewAreaName] = useState('');
  const [newTopicName, setNewTopicName] = useState('');
  const [newUnitName, setNewUnitName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Edit dialog state
  const [isEditAreaDialogOpen, setIsEditAreaDialogOpen] = useState(false);
  const [isEditTopicDialogOpen, setIsEditTopicDialogOpen] = useState(false);
  const [isEditUnitDialogOpen, setIsEditUnitDialogOpen] = useState(false);
  const [editingArea, setEditingArea] = useState<KnowledgeArea | null>(null);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);
  const [editAreaName, setEditAreaName] = useState('');
  const [editTopicName, setEditTopicName] = useState('');
  const [editUnitName, setEditUnitName] = useState('');

  // Delete dialog state
  const [isDeleteAreaDialogOpen, setIsDeleteAreaDialogOpen] = useState(false);
  const [isDeleteTopicDialogOpen, setIsDeleteTopicDialogOpen] = useState(false);
  const [isDeleteUnitDialogOpen, setIsDeleteUnitDialogOpen] = useState(false);
  const [deletingArea, setDeletingArea] = useState<KnowledgeArea | null>(null);
  const [deletingTopic, setDeletingTopic] = useState<Topic | null>(null);
  const [deletingUnit, setDeletingUnit] = useState<Unit | null>(null);

  // Fetch knowledge areas on component mount
  useEffect(() => {
    fetchKnowledgeAreas();
  }, []);

  // Fetch topics when area is selected
  useEffect(() => {
    if (selectedArea) {
      fetchTopics(selectedArea);
      setSelectedTopic(''); // Reset topic selection
      setUnits([]); // Clear units
    }
  }, [selectedArea]);

  // Fetch units when topic is selected
  useEffect(() => {
    if (selectedTopic) {
      fetchUnits(selectedTopic);
    }
  }, [selectedTopic]);

  const fetchKnowledgeAreas = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/knowledge-areas');
      setKnowledgeAreas(response);
    } catch (error) {
      console.error('Error fetching knowledge areas:', error);
      toast({
        title: "Error",
        description: "Failed to fetch knowledge areas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchTopics = async (areaId: string) => {
    try {
      setLoading(true);
      // Assuming there's an endpoint for topics by area
      const response = await apiService.get(`/knowledge-areas/${areaId}/knowledge-topics`);
      setTopics(response);
    } catch (error) {
      console.error('Error fetching topics:', error);
      toast({
        title: "Error",
        description: "Failed to fetch topics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUnits = async (topicId: string) => {
    try {
      setLoading(true);
      // Assuming there's an endpoint for units by topic
      const response = await apiService.get(`/knowledge-topics/${topicId}/knowledge-units`);
      setUnits(response);
    } catch (error) {
      console.error('Error fetching units:', error);
      toast({
        title: "Error",
        description: "Failed to fetch units",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddKnowledgeArea = async () => {
    if (!newAreaName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a name for the knowledge area",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.post('/knowledge-areas', {
        name: newAreaName.trim(),
      });

      // Add the new area to the list
      setKnowledgeAreas(prev => [...prev, response]);

      // Reset form and close dialog
      setNewAreaName('');
      setIsAddAreaDialogOpen(false);

      toast({
        title: "Success",
        description: "Knowledge area added successfully",
      });
    } catch (error) {
      console.error('Error adding knowledge area:', error);
      toast({
        title: "Error",
        description: "Failed to add knowledge area",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddTopic = async () => {
    if (!newTopicName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a name for the topic",
        variant: "destructive",
      });
      return;
    }

    if (!selectedArea) {
      toast({
        title: "Error",
        description: "Please select a knowledge area first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.post(`/knowledge-areas/${selectedArea}/knowledge-topics`, {
        name: newTopicName.trim()
      });

      // Add the new topic to the list
      setTopics(prev => [...prev, response]);

      // Reset form and close dialog
      setNewTopicName('');
      setIsAddTopicDialogOpen(false);

      toast({
        title: "Success",
        description: "Topic added successfully",
      });
    } catch (error) {
      console.error('Error adding topic:', error);
      toast({
        title: "Error",
        description: "Failed to add topic",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddUnit = async () => {
    if (!newUnitName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a name for the unit",
        variant: "destructive",
      });
      return;
    }

    if (!selectedTopic) {
      toast({
        title: "Error",
        description: "Please select a topic first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.post(`/knowledge-topics/${selectedTopic}/knowledge-units`, {
        name: newUnitName.trim()
      });

      // Add the new unit to the list
      setUnits(prev => [...prev, response]);

      // Reset form and close dialog
      setNewUnitName('');
      setIsAddUnitDialogOpen(false);

      toast({
        title: "Success",
        description: "Unit added successfully",
      });
    } catch (error) {
      console.error('Error adding unit:', error);
      toast({
        title: "Error",
        description: "Failed to add unit",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Edit functions
  const handleEditArea = (area: KnowledgeArea) => {
    setEditingArea(area);
    setEditAreaName(area.name);
    setIsEditAreaDialogOpen(true);
  };

  const handleEditTopic = (topic: Topic) => {
    setEditingTopic(topic);
    setEditTopicName(topic.name);
    setIsEditTopicDialogOpen(true);
  };

  const handleEditUnit = (unit: Unit) => {
    setEditingUnit(unit);
    setEditUnitName(unit.name);
    setIsEditUnitDialogOpen(true);
  };

  const handleUpdateArea = async () => {
    if (!editAreaName.trim() || !editingArea) {
      toast({
        title: "Error",
        description: "Please enter a name for the knowledge area",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.put(`/knowledge-areas/${editingArea.id}`, {
        name: editAreaName.trim(),
      });

      // Update the area in the list
      setKnowledgeAreas(prev => prev.map(area =>
        area.id === editingArea.id ? response : area
      ));

      // Reset form and close dialog
      setEditAreaName('');
      setEditingArea(null);
      setIsEditAreaDialogOpen(false);

      toast({
        title: "Success",
        description: "Knowledge area updated successfully",
      });
    } catch (error) {
      console.error('Error updating knowledge area:', error);
      toast({
        title: "Error",
        description: "Failed to update knowledge area",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateTopic = async () => {
    if (!editTopicName.trim() || !editingTopic) {
      toast({
        title: "Error",
        description: "Please enter a name for the topic",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.put(`/knowledge-topics/${editingTopic.id}`, {
        name: editTopicName.trim(),
      });

      // Update the topic in the list
      setTopics(prev => prev.map(topic =>
        topic.id === editingTopic.id ? response : topic
      ));

      // Reset form and close dialog
      setEditTopicName('');
      setEditingTopic(null);
      setIsEditTopicDialogOpen(false);

      toast({
        title: "Success",
        description: "Topic updated successfully",
      });
    } catch (error) {
      console.error('Error updating topic:', error);
      toast({
        title: "Error",
        description: "Failed to update topic",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateUnit = async () => {
    if (!editUnitName.trim() || !editingUnit) {
      toast({
        title: "Error",
        description: "Please enter a name for the unit",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await apiService.put(`/knowledge-units/${editingUnit.id}`, {
        name: editUnitName.trim(),
      });

      // Update the unit in the list
      setUnits(prev => prev.map(unit =>
        unit.id === editingUnit.id ? response : unit
      ));

      // Reset form and close dialog
      setEditUnitName('');
      setEditingUnit(null);
      setIsEditUnitDialogOpen(false);

      toast({
        title: "Success",
        description: "Unit updated successfully",
      });
    } catch (error) {
      console.error('Error updating unit:', error);
      toast({
        title: "Error",
        description: "Failed to update unit",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete functions
  const handleDeleteArea = (area: KnowledgeArea) => {
    setDeletingArea(area);
    setIsDeleteAreaDialogOpen(true);
  };

  const handleDeleteTopic = (topic: Topic) => {
    setDeletingTopic(topic);
    setIsDeleteTopicDialogOpen(true);
  };

  const handleDeleteUnit = (unit: Unit) => {
    setDeletingUnit(unit);
    setIsDeleteUnitDialogOpen(true);
  };

  const handleCurateUnit = (unit: Unit) => {
    toast({
      title: "Curate Unit",
      description: `Opening curation interface for "${unit.name}"`,
    });
    // Navigate to unit curation page
    navigate(`/apps/knowledge/curate?unitId=${unit.id}`);
  };

  const handleConfirmDeleteArea = async () => {
    if (!deletingArea) return;

    try {
      setIsSubmitting(true);
      await apiService.delete(`/knowledge-areas/${deletingArea.id}`);

      // Remove the area from the list
      setKnowledgeAreas(prev => prev.filter(area => area.id !== deletingArea.id));

      // Clear selection if the deleted area was selected
      if (selectedArea === deletingArea.id) {
        setSelectedArea('');
        setTopics([]);
        setSelectedTopic('');
        setUnits([]);
      }

      // Reset state and close dialog
      setDeletingArea(null);
      setIsDeleteAreaDialogOpen(false);

      toast({
        title: "Success",
        description: "Knowledge area deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting knowledge area:', error);
      toast({
        title: "Error",
        description: "Failed to delete knowledge area",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDeleteTopic = async () => {
    if (!deletingTopic) return;

    try {
      setIsSubmitting(true);
      await apiService.delete(`/knowledge-topics/${deletingTopic.id}`);

      // Remove the topic from the list
      setTopics(prev => prev.filter(topic => topic.id !== deletingTopic.id));

      // Clear selection if the deleted topic was selected
      if (selectedTopic === deletingTopic.id) {
        setSelectedTopic('');
        setUnits([]);
      }

      // Reset state and close dialog
      setDeletingTopic(null);
      setIsDeleteTopicDialogOpen(false);

      toast({
        title: "Success",
        description: "Topic deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting topic:', error);
      toast({
        title: "Error",
        description: "Failed to delete topic",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDeleteUnit = async () => {
    if (!deletingUnit) return;

    try {
      setIsSubmitting(true);
      await apiService.delete(`/knowledge-units/${deletingUnit.id}`);

      // Remove the unit from the list
      setUnits(prev => prev.filter(unit => unit.id !== deletingUnit.id));

      // Reset state and close dialog
      setDeletingUnit(null);
      setIsDeleteUnitDialogOpen(false);

      toast({
        title: "Success",
        description: "Unit deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting unit:', error);
      toast({
        title: "Error",
        description: "Failed to delete unit",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Knowledge"
          description="Organize and deliver structured training and knowledge resources across the enterprise"
        />
        <Button className="flex items-center gap-1" onClick={() => 
          toast({ 
            title: "Add Resource", 
            description: "Resource creation functionality will be implemented soon." 
          })
        }>
          <Plus className="h-4 w-4" /> Add Resource
        </Button>
      </div>

      <Tabs defaultValue="library" className="w-full">
        <TabsList className="grid w-full md:w-[800px] grid-cols-4">
          <TabsTrigger value="library">Knowledge Library</TabsTrigger>
          <TabsTrigger value="curate">Curate</TabsTrigger>
          <TabsTrigger value="my-training">My Training</TabsTrigger>
          <TabsTrigger value="team-learning">Team Learning</TabsTrigger>
        </TabsList>
        
        <TabsContent value="library" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search knowledge resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Safety Procedures</CardTitle>
                  <BookMarked className="h-5 w-5 text-blue-500" />
                </div>
                <CardDescription>Standard Operating Procedures</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Comprehensive guide to safety procedures for all operational activities.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Safety</Badge>
                  <Badge variant="outline">Procedures</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Equipment Training</CardTitle>
                  <BookMarked className="h-5 w-5 text-green-500" />
                </div>
                <CardDescription>Operational Training</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Training materials for safe operation of all facility equipment.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Training</Badge>
                  <Badge variant="outline">Equipment</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Regulatory Compliance</CardTitle>
                  <BookMarked className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Legal & Compliance</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Documentation on regulatory requirements and compliance procedures.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Compliance</Badge>
                  <Badge variant="outline">Regulatory</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="curate" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
            {/* Area Column */}
            <Card className="flex flex-col h-full">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FolderOpen className="h-5 w-5 text-blue-500" />
                    <CardTitle className="text-lg">Knowledge Areas</CardTitle>
                  </div>
                  <Dialog open={isAddAreaDialogOpen} onOpenChange={setIsAddAreaDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Plus className="h-3 w-3" />
                        Add
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Add New Knowledge Area</DialogTitle>
                        <DialogDescription>
                          Create a new knowledge area to organize topics and learning units.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="area-name" className="text-right">
                            Name
                          </Label>
                          <Input
                            id="area-name"
                            value={newAreaName}
                            onChange={(e) => setNewAreaName(e.target.value)}
                            placeholder="Enter knowledge area name"
                            className="col-span-3"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleAddKnowledgeArea();
                              }
                            }}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsAddAreaDialogOpen(false);
                            setNewAreaName('');
                          }}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleAddKnowledgeArea}
                          disabled={isSubmitting || !newAreaName.trim()}
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Adding...
                            </>
                          ) : (
                            'Add Area'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
                <CardDescription>Select a knowledge area to explore topics</CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-3 max-h-[480px]">
                {loading && knowledgeAreas.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p className="text-sm text-muted-foreground mt-3">Loading areas...</p>
                  </div>
                ) : knowledgeAreas.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <FolderOpen className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground">No knowledge areas available</p>
                    <p className="text-xs text-muted-foreground mt-1">Click "Add" to create your first area</p>
                  </div>
                ) : (
                  knowledgeAreas.map((area) => (
                    <div
                      key={area.id}
                      className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${
                        selectedArea === area.id
                          ? 'border-blue-500 bg-blue-50 shadow-sm ring-1 ring-blue-200'
                          : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <div
                          className="flex items-center gap-2 flex-1 cursor-pointer"
                          onClick={() => setSelectedArea(area.id)}
                        >
                          <div className={`w-2 h-2 rounded-full ${selectedArea === area.id ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                          <h4 className="font-semibold text-sm text-gray-900">{area.name}</h4>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreVertical className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditArea(area)}>
                              <Edit className="h-3 w-3 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteArea(area)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-3 w-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {area.description && (
                        <p className="text-xs text-muted-foreground ml-4">{area.description}</p>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Topic Column */}
            <Card className="flex flex-col h-full">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-green-500" />
                    <CardTitle className="text-lg">Topics</CardTitle>
                  </div>
                  <Dialog open={isAddTopicDialogOpen} onOpenChange={setIsAddTopicDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        disabled={!selectedArea}
                      >
                        <Plus className="h-3 w-3" />
                        Add
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Add New Topic</DialogTitle>
                        <DialogDescription>
                          Create a new topic under the selected knowledge area.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="topic-name" className="text-right">
                            Name
                          </Label>
                          <Input
                            id="topic-name"
                            value={newTopicName}
                            onChange={(e) => setNewTopicName(e.target.value)}
                            placeholder="Enter topic name"
                            className="col-span-3"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleAddTopic();
                              }
                            }}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsAddTopicDialogOpen(false);
                            setNewTopicName('');
                          }}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleAddTopic}
                          disabled={isSubmitting || !newTopicName.trim()}
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Adding...
                            </>
                          ) : (
                            'Add Topic'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
                <CardDescription>
                  {selectedArea ? 'Select a topic to view units' : 'Choose an area first'}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-3 max-h-[480px]">
                {!selectedArea ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <Target className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground">Select a knowledge area to view topics</p>
                    <p className="text-xs text-muted-foreground mt-1">Choose an area from the left column</p>
                  </div>
                ) : loading ? (
                  <div className="flex flex-col items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                    <p className="text-sm text-muted-foreground mt-3">Loading topics...</p>
                  </div>
                ) : topics.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <Target className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground">No topics available for this area</p>
                    <p className="text-xs text-muted-foreground mt-1">Click "Add" to create your first topic</p>
                  </div>
                ) : (
                  topics.map((topic) => (
                    <div
                      key={topic.id}
                      className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${
                        selectedTopic === topic.id
                          ? 'border-green-500 bg-green-50 shadow-sm ring-1 ring-green-200'
                          : 'border-gray-200 hover:border-green-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <div
                          className="flex items-center gap-2 flex-1 cursor-pointer"
                          onClick={() => setSelectedTopic(topic.id)}
                        >
                          <div className={`w-2 h-2 rounded-full ${selectedTopic === topic.id ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <h4 className="font-semibold text-sm text-gray-900">{topic.name}</h4>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreVertical className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditTopic(topic)}>
                              <Edit className="h-3 w-3 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteTopic(topic)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-3 w-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {topic.description && (
                        <p className="text-xs text-muted-foreground ml-4">{topic.description}</p>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Unit Column */}
            <Card className="flex flex-col h-full">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building className="h-5 w-5 text-purple-500" />
                    <CardTitle className="text-lg">Units</CardTitle>
                  </div>
                  <Dialog open={isAddUnitDialogOpen} onOpenChange={setIsAddUnitDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        disabled={!selectedTopic}
                      >
                        <Plus className="h-3 w-3" />
                        Add
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Add New Unit</DialogTitle>
                        <DialogDescription>
                          Create a new learning unit under the selected topic.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="unit-name" className="text-right">
                            Name
                          </Label>
                          <Input
                            id="unit-name"
                            value={newUnitName}
                            onChange={(e) => setNewUnitName(e.target.value)}
                            placeholder="Enter unit name"
                            className="col-span-3"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleAddUnit();
                              }
                            }}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsAddUnitDialogOpen(false);
                            setNewUnitName('');
                          }}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleAddUnit}
                          disabled={isSubmitting || !newUnitName.trim()}
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Adding...
                            </>
                          ) : (
                            'Add Unit'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
                <CardDescription>
                  {selectedTopic ? 'Learning units for this topic' : 'Choose a topic first'}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto space-y-3 max-h-[480px]">
                {!selectedTopic ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <Building className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground">Select a topic to view units</p>
                    <p className="text-xs text-muted-foreground mt-1">Choose a topic from the middle column</p>
                  </div>
                ) : loading ? (
                  <div className="flex flex-col items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    <p className="text-sm text-muted-foreground mt-3">Loading units...</p>
                  </div>
                ) : units.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <Building className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground">No units available for this topic</p>
                    <p className="text-xs text-muted-foreground mt-1">Click "Add" to create your first unit</p>
                  </div>
                ) : (
                  units.map((unit) => (
                    <div
                      key={unit.id}
                      className="p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-400"></div>
                          <h4 className="font-semibold text-sm text-gray-900">{unit.name}</h4>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreVertical className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditUnit(unit)}>
                              <Edit className="h-3 w-3 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCurateUnit(unit)}>
                              <Settings className="h-3 w-3 mr-2" />
                              Curate
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteUnit(unit)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-3 w-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {unit.description && (
                        <p className="text-xs text-muted-foreground mb-2 ml-4">{unit.description}</p>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>
          </div>

          {/* Edit Dialogs */}
          {/* Edit Knowledge Area Dialog */}
          <Dialog open={isEditAreaDialogOpen} onOpenChange={setIsEditAreaDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Knowledge Area</DialogTitle>
                <DialogDescription>
                  Update the knowledge area information.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-area-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-area-name"
                    value={editAreaName}
                    onChange={(e) => setEditAreaName(e.target.value)}
                    placeholder="Enter knowledge area name"
                    className="col-span-3"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateArea();
                      }
                    }}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditAreaDialogOpen(false);
                    setEditAreaName('');
                    setEditingArea(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateArea}
                  disabled={isSubmitting || !editAreaName.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Area'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Topic Dialog */}
          <Dialog open={isEditTopicDialogOpen} onOpenChange={setIsEditTopicDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Topic</DialogTitle>
                <DialogDescription>
                  Update the topic information.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-topic-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-topic-name"
                    value={editTopicName}
                    onChange={(e) => setEditTopicName(e.target.value)}
                    placeholder="Enter topic name"
                    className="col-span-3"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateTopic();
                      }
                    }}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditTopicDialogOpen(false);
                    setEditTopicName('');
                    setEditingTopic(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateTopic}
                  disabled={isSubmitting || !editTopicName.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Topic'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Unit Dialog */}
          <Dialog open={isEditUnitDialogOpen} onOpenChange={setIsEditUnitDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Unit</DialogTitle>
                <DialogDescription>
                  Update the learning unit information.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-unit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-unit-name"
                    value={editUnitName}
                    onChange={(e) => setEditUnitName(e.target.value)}
                    placeholder="Enter unit name"
                    className="col-span-3"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateUnit();
                      }
                    }}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditUnitDialogOpen(false);
                    setEditUnitName('');
                    setEditingUnit(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateUnit}
                  disabled={isSubmitting || !editUnitName.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Unit'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialogs */}
          {/* Delete Knowledge Area Dialog */}
          <Dialog open={isDeleteAreaDialogOpen} onOpenChange={setIsDeleteAreaDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Delete Knowledge Area</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete "{deletingArea?.name}"? This action cannot be undone and will also delete all associated topics and units.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteAreaDialogOpen(false);
                    setDeletingArea(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDeleteArea}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete Area'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Topic Dialog */}
          <Dialog open={isDeleteTopicDialogOpen} onOpenChange={setIsDeleteTopicDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Delete Topic</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete "{deletingTopic?.name}"? This action cannot be undone and will also delete all associated units.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteTopicDialogOpen(false);
                    setDeletingTopic(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDeleteTopic}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete Topic'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Unit Dialog */}
          <Dialog open={isDeleteUnitDialogOpen} onOpenChange={setIsDeleteUnitDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Delete Unit</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete "{deletingUnit?.name}"? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteUnitDialogOpen(false);
                    setDeletingUnit(null);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDeleteUnit}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete Unit'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        <TabsContent value="my-training" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">Annual Safety Refresher</CardTitle>
                    <CardDescription>Required Training</CardDescription>
                  </div>
                  <Badge className="bg-red-500">Due Soon</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Due in 5 days</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Estimated time: 45 minutes</span>
                  </div>
                  <div className="pt-2">
                    <Button variant="default" size="sm" className="w-full flex items-center justify-center gap-1">
                      <BookOpen className="h-3 w-3" /> Start Training
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">New Equipment Orientation</CardTitle>
                    <CardDescription>Optional Training</CardDescription>
                  </div>
                  <Badge className="bg-blue-500">New</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Available until June 30, 2023</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Estimated time: 30 minutes</span>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <BookOpen className="h-3 w-3" /> View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="team-learning" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Team Learning</h2>
            <p className="text-muted-foreground">
              This tab will display team learning progress and assigned training materials.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default KnowledgePage;
