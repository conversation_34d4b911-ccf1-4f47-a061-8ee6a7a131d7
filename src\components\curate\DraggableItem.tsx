import React from "react";
import { DraggableItemProps } from "@/types/curate";
import { motion } from "framer-motion";

const DraggableItem: React.FC<DraggableItemProps> = ({
  type,
  icon,
  label,
  description,
  onDragStart,
}) => {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("componentType", type);
    onDragStart(type);
  };

  return (
    <motion.div
      className="p-3 border border-slate-200 dark:border-slate-700 rounded-lg mb-2 cursor-grab bg-white dark:bg-slate-800 hover:border-primary hover:shadow-sm transition-all duration-200"
      draggable
      onDragStart={handleDragStart}
      whileHover={{ scale: 1.02, y: -1 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center">
        <div className="mr-3 p-2.5 bg-slate-100 dark:bg-slate-700 rounded-md text-primary">
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-sm text-slate-800 dark:text-slate-200">{label}</h3>
          {description && (
            <p className="text-xs text-muted-foreground mt-0.5 line-clamp-2">{description}</p>
          )}
        </div>
        <div className="ml-2 text-slate-400 dark:text-slate-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 4a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"></path>
            <path d="M14 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"></path>
            <path d="M14 20a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"></path>
          </svg>
        </div>
      </div>
    </motion.div>
  );
};

export default DraggableItem;
