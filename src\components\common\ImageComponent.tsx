import React, { useState, useEffect } from 'react';
import { fetchFullImageUrl, fetchDataUrlForImage } from '@/utils/imageUtils';
import {
  Loader2,
  FileText,
  FileSpreadsheet,
  File,
  FileImage,
  FileCode,
  FileJson,
  FileArchive,
  FileAudio,
  FileVideo
} from 'lucide-react';
import GalleryPage from './GalleryPage';

interface ImageComponentProps {
  fileName: string;
  size?: string;
  name?: boolean;
  onImageClick?: () => void;
}

const ImageComponent: React.FC<ImageComponentProps> = ({ fileName, size, name = false, onImageClick }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [dataUrl, setDataUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getFileUrl = async () => {
      try {
        setIsLoading(true);
        const url = await fetchFullImageUrl(fileName);
        setFileUrl(url);




        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching file or data URL:', error);
        setError('Failed to load image');
        setIsLoading(false);
      }
    };

    if (fileName) {
      getFileUrl();
    }
  }, [fileName]);

  const cleanFileName = (fileName: string) => {
    return fileName.replace(/^\d+[\s-_]*/, '');
  };

  const checkFileType = (fileName: string) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const pdfExtensions = ['pdf'];
    const xlsExtensions = ['xls', 'xlsx', 'csv'];
    const codeExtensions = ['js', 'jsx', 'ts', 'tsx', 'html', 'css', 'php', 'py', 'java', 'c', 'cpp', 'cs'];
    const jsonExtensions = ['json', 'xml'];
    const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'flac', 'm4a'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'mkv', 'webm'];
    const docExtensions = ['doc', 'docx', 'txt', 'rtf', 'odt'];

    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    if (imageExtensions.includes(extension)) {
      return 'image';
    } else if (pdfExtensions.includes(extension)) {
      return 'pdf';
    } else if (xlsExtensions.includes(extension)) {
      return 'xls';
    } else if (codeExtensions.includes(extension)) {
      return 'code';
    } else if (jsonExtensions.includes(extension)) {
      return 'json';
    } else if (archiveExtensions.includes(extension)) {
      return 'archive';
    } else if (audioExtensions.includes(extension)) {
      return 'audio';
    } else if (videoExtensions.includes(extension)) {
      return 'video';
    } else if (docExtensions.includes(extension)) {
      return 'doc';
    } else {
      return 'other';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error || !fileUrl) {
    return <p className="text-destructive">Error loading file</p>;
  }

  const fileType = checkFileType(fileName);
  const cleanedFileName = cleanFileName(fileName);

  // Function to handle file opening
  const handleFileOpen = () => {
    if (fileUrl) {
      // If onImageClick is provided, use it, otherwise open in new tab
      if (onImageClick && fileType === 'image') {
        onImageClick();
      } else {
        window.open(fileUrl, '_blank', 'noopener,noreferrer');
      }
    }
  };

  // Define the FileIcon component props
  interface FileIconProps {
    icon: React.ElementType;
    color: string;
    onClick: () => void;
  }

  // Reusable file icon component
  const FileIcon: React.FC<FileIconProps> = ({ icon: Icon, color, onClick }) => (
    <div
      className="cursor-pointer p-1 rounded hover:bg-gray-100 transition-colors flex items-center justify-center"
      onClick={onClick}
    >
      <Icon className={`h-8 w-8 ${color}`} />
    </div>
  );

  switch (fileType) {
    case 'image':
      return (
        <div className="flex flex-col items-center p-2 image-container">
          { fileUrl ? (
            <div className={`w-full image-wrapper ${size ? `h-${size} w-${size}` : ''}`} >
              {onImageClick ? (
                // If onImageClick is provided, use a simple image without lightbox
                <img
                  src={fileUrl}
                  alt={cleanedFileName}
                  className="w-full h-full object-cover cursor-pointer rounded-md"
                  onClick={onImageClick}
                />
              ) : (
                // If no onImageClick, use GalleryPage with its own lightbox
                <GalleryPage
                  photos={[{
                    src: fileUrl || '',
                    width: 4,
                    height: 3
                  }]}
                  width={size}
                />
              )}
            </div>
          ) : (
            <FileIcon
              icon={FileImage}
              color="text-blue-400"
              onClick={handleFileOpen}
            />
          )}
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'pdf':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileText}
              color="text-red-500"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'xls':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileSpreadsheet}
              color="text-green-600"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'code':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileCode}
              color="text-purple-600"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'json':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileJson}
              color="text-yellow-600"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'archive':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileArchive}
              color="text-amber-700"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'audio':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileAudio}
              color="text-pink-500"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'video':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          {fileUrl ? (
            <div className="w-full max-w-md">
              <video
                controls
                className="w-full h-auto rounded border"
                preload="metadata"
                onClick={onImageClick}
                style={{ cursor: onImageClick ? 'pointer' : 'default' }}
              >
                <source src={fileUrl} type="video/mp4" />
                <source src={fileUrl} type="video/webm" />
                <source src={fileUrl} type="video/ogg" />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className="file-icon-wrapper">
              <FileIcon
                icon={FileVideo}
                color="text-indigo-500"
                onClick={handleFileOpen}
              />
            </div>
          )}
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'doc':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={FileText}
              color="text-blue-600"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    case 'other':
      return (
        <div className="flex flex-col items-center p-2 file-container">
          <div className="file-icon-wrapper">
            <FileIcon
              icon={File}
              color="text-gray-500"
              onClick={handleFileOpen}
            />
          </div>
          {name && (
            <p className="mt-2 text-sm text-center break-words">{cleanedFileName}</p>
          )}
        </div>
      );
    default:
      return <p>Unknown file type</p>;
  }
};

export default ImageComponent;
