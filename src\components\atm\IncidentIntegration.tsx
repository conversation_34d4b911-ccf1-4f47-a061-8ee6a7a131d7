import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertTriangle,
  ExternalLink,
  Plus,
  FileText,
  Clock,
  User,
  MapPin
} from 'lucide-react';

interface IncidentIntegrationProps {
  assetId?: string;
  onCreateIncident?: (assetId: string) => void;
}

const IncidentIntegration = ({ assetId, onCreateIncident }: IncidentIntegrationProps) => {
  const [relatedIncidents, setRelatedIncidents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    if (assetId) {
      loadRelatedIncidents();
    }
  }, [assetId]);

  const loadRelatedIncidents = async () => {
    try {
      setIsLoading(true);
      // Mock data for demo - in real implementation, this would fetch from incident API
      setRelatedIncidents(getMockIncidents());
    } catch (error) {
      console.error('Error loading related incidents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMockIncidents = () => [
    {
      id: 'INC-001',
      title: 'Equipment Malfunction - HVAC Unit A1',
      description: 'Unusual noise and vibration detected',
      severity: 'Medium',
      status: 'Under Investigation',
      reportedBy: 'John Doe',
      reportedAt: '2024-02-10T14:30:00Z',
      location: 'Building A - Roof',
      assetId: '1'
    },
    {
      id: 'INC-002',
      title: 'Pressure Vessel Safety Concern',
      description: 'Pressure reading exceeded normal range',
      severity: 'High',
      status: 'Resolved',
      reportedBy: 'Jane Smith',
      reportedAt: '2024-02-05T09:15:00Z',
      location: 'Plant Floor - Section B',
      assetId: '2'
    }
  ];

  const getSeverityBadge = (severity: string) => {
    const variants: { [key: string]: any } = {
      'Low': 'outline',
      'Medium': 'secondary',
      'High': 'destructive',
      'Critical': 'destructive'
    };
    return <Badge variant={variants[severity] || 'outline'}>{severity}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: any } = {
      'Open': 'destructive',
      'Under Investigation': 'default',
      'Resolved': 'secondary',
      'Closed': 'outline'
    };
    return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
  };

  const handleCreateIncident = () => {
    if (onCreateIncident && assetId) {
      onCreateIncident(assetId);
    } else {
      // Navigate to incident creation page
      window.open('/apps/investigation', '_blank');
    }
  };

  const handleViewIncident = (incidentId: string) => {
    // Navigate to incident details page
    window.open(`/apps/investigation?id=${incidentId}`, '_blank');
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Related Incidents</h3>
        <Button onClick={handleCreateIncident} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Report Incident
        </Button>
      </div>

      {/* Integration Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <ExternalLink className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Incident Reporting Integration</h4>
              <p className="text-sm text-blue-700 mt-1">
                Equipment-related incidents are automatically linked to asset records. 
                Create new incidents directly from asset maintenance activities.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Related Incidents */}
      {isLoading ? (
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      ) : relatedIncidents.length > 0 ? (
        <div className="space-y-3">
          {relatedIncidents.map(incident => (
            <Card key={incident.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                      <span className="font-medium">{incident.id}</span>
                      {getSeverityBadge(incident.severity)}
                      {getStatusBadge(incident.status)}
                    </div>
                    
                    <h4 className="font-medium mb-1">{incident.title}</h4>
                    <p className="text-sm text-muted-foreground mb-3">{incident.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>Reported by {incident.reportedBy}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(incident.reportedAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{incident.location}</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewIncident(incident.id)}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <h4 className="font-medium mb-2">No Related Incidents</h4>
            <p className="text-sm text-muted-foreground mb-4">
              No incidents have been reported for this asset yet.
            </p>
            <Button onClick={handleCreateIncident} variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Report First Incident
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button 
            variant="outline" 
            className="w-full justify-start" 
            onClick={handleCreateIncident}
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Report Equipment Incident
          </Button>
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={() => window.open('/apps/investigation', '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View All Incidents
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default IncidentIntegration;
