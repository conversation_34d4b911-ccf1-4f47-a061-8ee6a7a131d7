import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { useToast } from '@/components/ui/use-toast';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import {
  Plus,
  Calendar as CalendarIcon,
  Clock,
  User,
  Wrench,
  AlertTriangle,
  CheckCircle,
  Filter,
  Download
} from 'lucide-react';
import { MaintenanceRecord, MaintenanceFilter, MaintenanceStatus, MaintenanceType, MaintenancePriority } from '@/types/atm';
import { fetchMaintenanceRecords } from '@/services/atmApi';

interface MaintenanceSchedulingProps {
  searchQuery: string;
}

const MaintenanceScheduling = ({ searchQuery }: MaintenanceSchedulingProps) => {
  const [maintenanceRecords, setMaintenanceRecords] = useState<MaintenanceRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<MaintenanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [filter, setFilter] = useState<MaintenanceFilter>({});
  const { toast } = useToast();

  useEffect(() => {
    loadMaintenanceRecords();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [maintenanceRecords, searchQuery, filter]);

  const loadMaintenanceRecords = async () => {
    try {
      setIsLoading(true);
      const data = await fetchMaintenanceRecords();
      setMaintenanceRecords(data);
    } catch (error) {
      console.error('Error loading maintenance records:', error);
      // Use mock data for demo
      setMaintenanceRecords(getMockMaintenanceRecords());
      toast({
        title: "Demo Mode",
        description: "Using mock maintenance data for demonstration",
        variant: "default"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getMockMaintenanceRecords = (): MaintenanceRecord[] => [
    {
      id: '1',
      assetId: '1',
      type: MaintenanceType.PREVENTIVE,
      title: 'HVAC Filter Replacement',
      description: 'Replace air filters and inspect ductwork',
      scheduledDate: '2024-02-15T09:00:00Z',
      dueDate: '2024-02-15T17:00:00Z',
      status: MaintenanceStatus.SCHEDULED,
      priority: MaintenancePriority.MEDIUM,
      assignedTo: ['tech1', 'tech2'],
      estimatedDuration: 4,
      cost: 250,
      parts: [
        { id: '1', name: 'HEPA Filter', partNumber: 'HF-001', quantity: 4, unitCost: 45, supplier: 'FilterCorp' }
      ],
      procedures: ['Turn off HVAC system', 'Remove old filters', 'Install new filters', 'Test system'],
      notes: 'Check for any unusual wear patterns',
      attachments: [],
      created: '2024-01-15T10:00:00Z',
      updated: '2024-01-20T14:30:00Z',
      createdBy: 'admin',
      updatedBy: 'maintenance_manager'
    },
    {
      id: '2',
      assetId: '2',
      type: MaintenanceType.CORRECTIVE,
      title: 'Pressure Vessel Inspection',
      description: 'Annual safety inspection and pressure testing',
      scheduledDate: '2024-02-20T08:00:00Z',
      dueDate: '2024-02-20T16:00:00Z',
      status: MaintenanceStatus.IN_PROGRESS,
      priority: MaintenancePriority.HIGH,
      assignedTo: ['inspector1'],
      estimatedDuration: 8,
      cost: 1200,
      parts: [],
      procedures: ['Pressure test', 'Visual inspection', 'Documentation', 'Certification'],
      notes: 'Requires certified inspector',
      attachments: [],
      created: '2024-01-10T09:00:00Z',
      updated: '2024-02-20T08:30:00Z',
      createdBy: 'admin',
      updatedBy: 'inspector1'
    },
    {
      id: '3',
      assetId: '3',
      type: MaintenanceType.EMERGENCY,
      title: 'Pump Bearing Replacement',
      description: 'Replace failed bearing in centrifugal pump',
      scheduledDate: '2024-02-10T14:00:00Z',
      completedDate: '2024-02-10T18:30:00Z',
      dueDate: '2024-02-10T20:00:00Z',
      status: MaintenanceStatus.COMPLETED,
      priority: MaintenancePriority.URGENT,
      assignedTo: ['tech3'],
      estimatedDuration: 6,
      actualDuration: 4.5,
      cost: 850,
      parts: [
        { id: '2', name: 'Ball Bearing', partNumber: 'BB-205', quantity: 2, unitCost: 125, supplier: 'BearingCorp' }
      ],
      procedures: ['Isolate pump', 'Remove impeller', 'Replace bearing', 'Reassemble', 'Test'],
      notes: 'Bearing showed signs of overheating',
      attachments: [],
      created: '2024-02-10T13:00:00Z',
      updated: '2024-02-10T19:00:00Z',
      createdBy: 'operator1',
      updatedBy: 'tech3'
    }
  ];

  const applyFilters = () => {
    let filtered = maintenanceRecords;

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(record =>
        record.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.assetId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply filters
    if (filter.type) {
      filtered = filtered.filter(record => record.type === filter.type);
    }
    if (filter.status) {
      filtered = filtered.filter(record => record.status === filter.status);
    }
    if (filter.priority) {
      filtered = filtered.filter(record => record.priority === filter.priority);
    }

    setFilteredRecords(filtered);
  };

  const getStatusBadge = (status: MaintenanceStatus) => {
    const variants = {
      [MaintenanceStatus.SCHEDULED]: 'outline',
      [MaintenanceStatus.IN_PROGRESS]: 'default',
      [MaintenanceStatus.COMPLETED]: 'secondary',
      [MaintenanceStatus.CANCELLED]: 'destructive',
      [MaintenanceStatus.OVERDUE]: 'destructive'
    };
    return <Badge variant={variants[status] as any}>{status}</Badge>;
  };

  const getPriorityBadge = (priority: MaintenancePriority) => {
    const variants = {
      [MaintenancePriority.LOW]: 'outline',
      [MaintenancePriority.MEDIUM]: 'secondary',
      [MaintenancePriority.HIGH]: 'destructive',
      [MaintenancePriority.URGENT]: 'destructive'
    };
    return <Badge variant={variants[priority] as any}>{priority}</Badge>;
  };

  const getTypeBadge = (type: MaintenanceType) => {
    const colors = {
      [MaintenanceType.PREVENTIVE]: 'text-blue-600 bg-blue-50',
      [MaintenanceType.CORRECTIVE]: 'text-orange-600 bg-orange-50',
      [MaintenanceType.PREDICTIVE]: 'text-green-600 bg-green-50',
      [MaintenanceType.EMERGENCY]: 'text-red-600 bg-red-50',
      [MaintenanceType.ROUTINE]: 'text-gray-600 bg-gray-50'
    };
    return <Badge className={colors[type]}>{type}</Badge>;
  };

  const columns = [
    {
      key: 'title',
      label: 'Maintenance Task',
      sortable: true,
      render: (record: MaintenanceRecord) => (
        <div>
          <div className="font-medium">{record.title}</div>
          <div className="text-sm text-muted-foreground">{record.description}</div>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
      render: (record: MaintenanceRecord) => getTypeBadge(record.type)
    },
    {
      key: 'scheduledDate',
      label: 'Scheduled Date',
      sortable: true,
      render: (record: MaintenanceRecord) => (
        <div className="flex items-center gap-1">
          <CalendarIcon className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{new Date(record.scheduledDate).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (record: MaintenanceRecord) => getStatusBadge(record.status)
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (record: MaintenanceRecord) => getPriorityBadge(record.priority)
    },
    {
      key: 'estimatedDuration',
      label: 'Duration',
      sortable: true,
      render: (record: MaintenanceRecord) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{record.estimatedDuration}h</span>
        </div>
      )
    },
    {
      key: 'cost',
      label: 'Cost',
      sortable: true,
      render: (record: MaintenanceRecord) => (
        <span className="text-sm">${record.cost?.toLocaleString() || 'N/A'}</span>
      )
    }
  ];

  const expandedRowRender = (record: MaintenanceRecord) => (
    <div className="p-4 bg-muted/50 rounded-lg space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <h4 className="font-medium mb-2">Task Details</h4>
          <div className="space-y-1 text-sm">
            <div><span className="font-medium">Asset ID:</span> {record.assetId}</div>
            <div><span className="font-medium">Assigned To:</span> {record.assignedTo.join(', ')}</div>
            <div><span className="font-medium">Due Date:</span> {new Date(record.dueDate).toLocaleString()}</div>
            {record.completedDate && (
              <div><span className="font-medium">Completed:</span> {new Date(record.completedDate).toLocaleString()}</div>
            )}
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Parts Required</h4>
          <div className="space-y-1 text-sm">
            {record.parts.length > 0 ? (
              record.parts.map(part => (
                <div key={part.id}>
                  {part.quantity}x {part.name} ({part.partNumber})
                </div>
              ))
            ) : (
              <div className="text-muted-foreground">No parts required</div>
            )}
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Procedures</h4>
          <div className="space-y-1 text-sm">
            {record.procedures.map((procedure, index) => (
              <div key={index} className="flex items-start gap-2">
                <span className="text-muted-foreground">{index + 1}.</span>
                <span>{procedure}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {record.notes && (
        <div>
          <h4 className="font-medium mb-2">Notes</h4>
          <p className="text-sm text-muted-foreground">{record.notes}</p>
        </div>
      )}
    </div>
  );

  const upcomingTasks = filteredRecords.filter(record => 
    record.status === MaintenanceStatus.SCHEDULED &&
    new Date(record.scheduledDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
  );

  const overdueTasks = filteredRecords.filter(record => 
    record.status !== MaintenanceStatus.COMPLETED &&
    new Date(record.dueDate) < new Date()
  );

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Schedule
          </Button>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Schedule Maintenance
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Upcoming (7 days)</p>
                <p className="text-2xl font-bold">{upcomingTasks.length}</p>
              </div>
              <CalendarIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{overdueTasks.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold">
                  {filteredRecords.filter(r => r.status === MaintenanceStatus.IN_PROGRESS).length}
                </p>
              </div>
              <Wrench className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed (30 days)</p>
                <p className="text-2xl font-bold">
                  {filteredRecords.filter(r => r.status === MaintenanceStatus.COMPLETED).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calendar and Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Maintenance Calendar</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Maintenance Schedule</CardTitle>
            <CardDescription>
              {filteredRecords.length} maintenance tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ExpandableDataTable
              data={filteredRecords}
              columns={columns}
              loading={isLoading}
              expandedRowRender={expandedRowRender}
              searchable={false}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MaintenanceScheduling;
